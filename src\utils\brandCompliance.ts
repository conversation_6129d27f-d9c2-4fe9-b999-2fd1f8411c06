/**
 * Brand Compliance Utilities
 * Functions for validating brand kit uploads and checking compliance
 */

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Validate brand kit files before upload
 */
export function validateBrandKit(files: FileList): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  };

  // Check if any files were provided
  if (files.length === 0) {
    result.isValid = false;
    result.errors.push('No files selected for upload');
    return result;
  }

  // Check file count limit
  if (files.length > 10) {
    result.isValid = false;
    result.errors.push('Maximum 10 files allowed per brand kit');
  }

  // Validate each file
  Array.from(files).forEach((file, index) => {
    const fileValidation = validateBrandAssetFile(file, index);
    
    if (!fileValidation.isValid) {
      result.isValid = false;
      result.errors.push(...fileValidation.errors);
    }
    
    result.warnings.push(...fileValidation.warnings);
  });

  // Check for recommended file types
  const hasLogo = Array.from(files).some(file => 
    file.name.toLowerCase().includes('logo') || 
    file.type === 'image/svg+xml'
  );
  
  if (!hasLogo) {
    result.warnings.push('Consider including a logo file for better brand analysis');
  }

  const hasColorReference = Array.from(files).some(file =>
    file.name.toLowerCase().includes('color') ||
    file.name.toLowerCase().includes('palette') ||
    file.type === 'application/pdf'
  );

  if (!hasColorReference) {
    result.warnings.push('Consider including color palette or brand guidelines document');
  }

  return result;
}

/**
 * Validate individual brand asset file
 */
function validateBrandAssetFile(file: File, index: number): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  };

  // Check file size (50MB limit)
  const maxSize = 50 * 1024 * 1024;
  if (file.size > maxSize) {
    result.isValid = false;
    result.errors.push(`File "${file.name}" exceeds 50MB size limit`);
  }

  // Check file type
  const allowedTypes = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/svg+xml',
    'application/pdf',
    'application/postscript', // .ai files
    'image/vnd.adobe.photoshop' // .psd files
  ];

  if (!allowedTypes.includes(file.type)) {
    result.isValid = false;
    result.errors.push(`File "${file.name}" has unsupported format: ${file.type}`);
  }

  // Check file name for potential issues
  if (file.name.includes(' ')) {
    result.warnings.push(`File "${file.name}" contains spaces - consider using underscores or hyphens`);
  }

  if (file.name.length > 100) {
    result.warnings.push(`File "${file.name}" has a very long filename`);
  }

  // Check for common brand asset patterns
  const fileName = file.name.toLowerCase();
  if (fileName.includes('logo') && !['image/svg+xml', 'image/png'].includes(file.type)) {
    result.warnings.push(`Logo file "${file.name}" should preferably be SVG or PNG format`);
  }

  return result;
}

/**
 * Extract brand information from file names and types
 */
export function extractBrandInfoFromFiles(files: FileList): {
  suggestedName: string;
  assetTypes: string[];
  confidence: number;
} {
  const fileNames = Array.from(files).map(f => f.name.toLowerCase());
  const fileTypes = Array.from(files).map(f => f.type);
  
  // Try to extract brand name from file names
  const commonWords = ['logo', 'brand', 'identity', 'kit', 'guide', 'palette', 'color'];
  let suggestedName = 'Brand Kit';
  
  // Look for company/brand name patterns
  fileNames.forEach(name => {
    const parts = name.split(/[-_\s.]/);
    const brandPart = parts.find(part => 
      part.length > 2 && 
      !commonWords.includes(part) &&
      !['png', 'jpg', 'jpeg', 'svg', 'pdf', 'ai', 'psd'].includes(part)
    );
    
    if (brandPart) {
      suggestedName = brandPart.charAt(0).toUpperCase() + brandPart.slice(1) + ' Brand Kit';
    }
  });

  // Identify asset types
  const assetTypes: string[] = [];
  
  if (fileTypes.some(type => type.startsWith('image/'))) {
    assetTypes.push('Visual Assets');
  }
  
  if (fileNames.some(name => name.includes('logo'))) {
    assetTypes.push('Logo');
  }
  
  if (fileNames.some(name => name.includes('color') || name.includes('palette'))) {
    assetTypes.push('Color Palette');
  }
  
  if (fileTypes.includes('application/pdf')) {
    assetTypes.push('Brand Guidelines');
  }
  
  if (fileTypes.includes('image/svg+xml')) {
    assetTypes.push('Vector Graphics');
  }

  // Calculate confidence based on file quality and completeness
  let confidence = 0.5; // Base confidence
  
  if (assetTypes.includes('Logo')) confidence += 0.2;
  if (assetTypes.includes('Color Palette')) confidence += 0.15;
  if (assetTypes.includes('Brand Guidelines')) confidence += 0.15;
  if (files.length >= 3) confidence += 0.1;
  if (fileTypes.includes('image/svg+xml')) confidence += 0.1;
  
  confidence = Math.min(confidence, 0.95); // Cap at 95%

  return {
    suggestedName,
    assetTypes,
    confidence
  };
}

/**
 * Check color accessibility compliance
 */
export function checkColorAccessibility(color1: string, color2: string): {
  contrastRatio: number;
  wcagAA: boolean;
  wcagAAA: boolean;
  recommendation: string;
} {
  const ratio = calculateContrastRatio(color1, color2);
  
  return {
    contrastRatio: ratio,
    wcagAA: ratio >= 4.5,
    wcagAAA: ratio >= 7,
    recommendation: ratio < 4.5 
      ? 'Increase contrast for better accessibility'
      : ratio < 7
      ? 'Good contrast for WCAG AA compliance'
      : 'Excellent contrast for WCAG AAA compliance'
  };
}

/**
 * Calculate contrast ratio between two colors
 */
function calculateContrastRatio(color1: string, color2: string): number {
  const lum1 = getRelativeLuminance(color1);
  const lum2 = getRelativeLuminance(color2);
  
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  
  return (brightest + 0.05) / (darkest + 0.05);
}

/**
 * Get relative luminance of a color
 */
function getRelativeLuminance(color: string): number {
  // Convert hex to RGB
  const hex = color.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16) / 255;
  const g = parseInt(hex.substr(2, 2), 16) / 255;
  const b = parseInt(hex.substr(4, 2), 16) / 255;
  
  // Apply gamma correction
  const rs = r <= 0.03928 ? r / 12.92 : Math.pow((r + 0.055) / 1.055, 2.4);
  const gs = g <= 0.03928 ? g / 12.92 : Math.pow((g + 0.055) / 1.055, 2.4);
  const bs = b <= 0.03928 ? b / 12.92 : Math.pow((b + 0.055) / 1.055, 2.4);
  
  // Calculate relative luminance
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
}

/**
 * Validate font accessibility
 */
export function validateFontAccessibility(fontSize: number, fontWeight: string): {
  isAccessible: boolean;
  recommendations: string[];
} {
  const recommendations: string[] = [];
  let isAccessible = true;
  
  // Check minimum font size
  if (fontSize < 12) {
    isAccessible = false;
    recommendations.push('Font size should be at least 12px for readability');
  }
  
  // Check font weight for small sizes
  if (fontSize < 14 && ['100', '200', '300', 'thin', 'light'].includes(fontWeight.toLowerCase())) {
    recommendations.push('Use medium or bold font weight for small text sizes');
  }
  
  // Recommendations for optimal readability
  if (fontSize >= 16) {
    recommendations.push('Good font size for body text');
  } else if (fontSize >= 14) {
    recommendations.push('Acceptable font size, consider increasing for better readability');
  }
  
  return {
    isAccessible,
    recommendations
  };
}

/**
 * Generate brand compliance score
 */
export function calculateBrandComplianceScore(
  colorCompliance: number,
  typographyCompliance: number,
  spacingCompliance: number,
  logoCompliance: number
): {
  overallScore: number;
  breakdown: Record<string, number>;
  grade: 'A' | 'B' | 'C' | 'D' | 'F';
} {
  const weights = {
    color: 0.3,
    typography: 0.25,
    spacing: 0.25,
    logo: 0.2
  };
  
  const overallScore = Math.round(
    colorCompliance * weights.color +
    typographyCompliance * weights.typography +
    spacingCompliance * weights.spacing +
    logoCompliance * weights.logo
  );
  
  let grade: 'A' | 'B' | 'C' | 'D' | 'F';
  if (overallScore >= 90) grade = 'A';
  else if (overallScore >= 80) grade = 'B';
  else if (overallScore >= 70) grade = 'C';
  else if (overallScore >= 60) grade = 'D';
  else grade = 'F';
  
  return {
    overallScore,
    breakdown: {
      color: colorCompliance,
      typography: typographyCompliance,
      spacing: spacingCompliance,
      logo: logoCompliance
    },
    grade
  };
}
