{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "noImplicitAny": false, "noImplicitReturns": false, "noUnusedLocals": false, "noUnusedParameters": false}, "include": ["src"], "exclude": ["node_modules", "dist", "server"]}