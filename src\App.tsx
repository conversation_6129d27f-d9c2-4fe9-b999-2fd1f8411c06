import React, { useState, useEffect } from 'react';
import { Flex, View, Heading, Text, ProgressBar, ActionButton } from '@adobe/react-spectrum';
import styled from 'styled-components';

// Components
import BrandKitUploader from './components/BrandKitUploader';
import PlatformOptimizer from './components/PlatformOptimizer';
import VariationGenerator from './components/VariationGenerator';
import ExportQueue from './components/ExportQueue';
import NavigationTabs from './components/NavigationTabs';

// Services
import { useBrandKit } from './hooks/useBrandKit';
import { useDocumentAPI } from './hooks/useDocumentAPI';
import { useAnalytics } from './hooks/useAnalytics';

// Types
interface AppConfig {
  apiEndpoint: string;
  openaiApiKey: string;
  environment: 'development' | 'production';
  features: {
    aiOptimization: boolean;
    socialMediaIntegration: boolean;
    analytics: boolean;
    collaboration: boolean;
  };
}

interface AppProps {
  config: AppConfig;
}

// Styled Components
const AppContainer = styled(View)`
  height: 100vh;
  background: ${props => props.theme.colors.background};
  overflow: hidden;
`;

const HeaderSection = styled(View)`
  padding: ${props => props.theme.spacing.md};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
`;

const ContentSection = styled(View)`
  flex: 1;
  overflow-y: auto;
  padding: ${props => props.theme.spacing.md};
`;

const StatusBar = styled(View)`
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  background: ${props => props.theme.colors.surface};
  border-top: 1px solid ${props => props.theme.colors.border};
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const FeatureCard = styled(View)`
  background: white;
  border-radius: ${props => props.theme.borderRadius.lg};
  box-shadow: ${props => props.theme.shadows.sm};
  margin-bottom: ${props => props.theme.spacing.md};
  overflow: hidden;
`;

// App State Types
type AppStep = 'brand-setup' | 'design-import' | 'optimization' | 'export';

interface AppState {
  currentStep: AppStep;
  brandKitUploaded: boolean;
  designImported: boolean;
  optimizationComplete: boolean;
  exportReady: boolean;
  processingStatus: string;
  progress: number;
}

const App: React.FC<AppProps> = ({ config }) => {
  // State Management
  const [appState, setAppState] = useState<AppState>({
    currentStep: 'brand-setup',
    brandKitUploaded: false,
    designImported: false,
    optimizationComplete: false,
    exportReady: false,
    processingStatus: 'Ready to start',
    progress: 0,
  });

  const [activeTab, setActiveTab] = useState<string>('brand-kit');

  // Custom Hooks
  const { brandKit, uploadBrandKit, validateBrandKit } = useBrandKit();
  const { currentDocument, documentElements, importDesign } = useDocumentAPI();
  const { trackEvent } = useAnalytics();

  // Effects
  useEffect(() => {
    // Track app initialization
    trackEvent('app_initialized', {
      environment: config.environment,
      features: config.features,
    });

    // Check if user has existing brand kit
    if (brandKit) {
      setAppState(prev => ({
        ...prev,
        brandKitUploaded: true,
        currentStep: 'design-import',
        progress: 25,
      }));
    }
  }, [config, brandKit, trackEvent]);

  useEffect(() => {
    // Update progress based on app state
    let newProgress = 0;
    if (appState.brandKitUploaded) newProgress += 25;
    if (appState.designImported) newProgress += 25;
    if (appState.optimizationComplete) newProgress += 25;
    if (appState.exportReady) newProgress += 25;

    setAppState(prev => ({
      ...prev,
      progress: newProgress,
    }));
  }, [appState.brandKitUploaded, appState.designImported, appState.optimizationComplete, appState.exportReady]);

  // Event Handlers
  const handleBrandKitUpload = async (files: FileList) => {
    try {
      setAppState(prev => ({ ...prev, processingStatus: 'Uploading brand kit...' }));
      
      await uploadBrandKit(files);
      
      setAppState(prev => ({
        ...prev,
        brandKitUploaded: true,
        currentStep: 'design-import',
        processingStatus: 'Brand kit uploaded successfully',
      }));

      trackEvent('brand_kit_uploaded', { fileCount: files.length });
    } catch (error) {
      console.error('Failed to upload brand kit:', error);
      setAppState(prev => ({ 
        ...prev, 
        processingStatus: 'Failed to upload brand kit. Please try again.' 
      }));
    }
  };

  const handleDesignImport = async () => {
    try {
      setAppState(prev => ({ ...prev, processingStatus: 'Importing design from document...' }));
      
      await importDesign();
      
      setAppState(prev => ({
        ...prev,
        designImported: true,
        currentStep: 'optimization',
        processingStatus: 'Design imported successfully',
      }));

      trackEvent('design_imported', { elementCount: documentElements?.length || 0 });
    } catch (error) {
      console.error('Failed to import design:', error);
      setAppState(prev => ({ 
        ...prev, 
        processingStatus: 'Failed to import design. Please try again.' 
      }));
    }
  };

  const handleOptimizationComplete = () => {
    setAppState(prev => ({
      ...prev,
      optimizationComplete: true,
      currentStep: 'export',
      processingStatus: 'Optimization complete',
    }));

    trackEvent('optimization_completed');
  };

  const handleExportReady = () => {
    setAppState(prev => ({
      ...prev,
      exportReady: true,
      processingStatus: 'Ready for export',
    }));

    trackEvent('export_ready');
  };

  // Tab Configuration
  const tabs = [
    {
      id: 'brand-kit',
      label: 'Brand Kit',
      icon: '🎨',
      disabled: false,
    },
    {
      id: 'optimization',
      label: 'Optimization',
      icon: '🤖',
      disabled: !appState.brandKitUploaded,
    },
    {
      id: 'variations',
      label: 'Variations',
      icon: '📱',
      disabled: !appState.designImported,
    },
    {
      id: 'export',
      label: 'Export',
      icon: '📤',
      disabled: !appState.optimizationComplete,
    },
  ];

  // Render Tab Content
  const renderTabContent = () => {
    switch (activeTab) {
      case 'brand-kit':
        return (
          <FeatureCard>
            <BrandKitUploader
              onUpload={handleBrandKitUpload}
              brandKit={brandKit}
              isUploaded={appState.brandKitUploaded}
            />
          </FeatureCard>
        );

      case 'optimization':
        return (
          <FeatureCard>
            <PlatformOptimizer
              brandKit={brandKit}
              currentDocument={currentDocument}
              onImportDesign={handleDesignImport}
              onOptimizationComplete={handleOptimizationComplete}
              isDesignImported={appState.designImported}
            />
          </FeatureCard>
        );

      case 'variations':
        return (
          <FeatureCard>
            <VariationGenerator
              brandKit={brandKit}
              documentElements={documentElements}
              onVariationsGenerated={handleExportReady}
              isOptimizationComplete={appState.optimizationComplete}
            />
          </FeatureCard>
        );

      case 'export':
        return (
          <FeatureCard>
            <ExportQueue
              variations={[]} // Will be populated by VariationGenerator
              onExportComplete={() => trackEvent('export_completed')}
              isExportReady={appState.exportReady}
            />
          </FeatureCard>
        );

      default:
        return null;
    }
  };

  return (
    <AppContainer>
      {/* Header */}
      <HeaderSection>
        <Flex direction="column" gap="size-100">
          <Flex justifyContent="space-between" alignItems="center">
            <Heading level={2}>BrandFlow Pro</Heading>
            <ActionButton 
              isQuiet 
              onPress={() => trackEvent('help_clicked')}
            >
              Help
            </ActionButton>
          </Flex>
          <Text>
            AI-powered brand consistency and multi-platform optimization
          </Text>
          <ProgressBar 
            label="Progress" 
            value={appState.progress} 
            maxValue={100}
            showValueLabel
          />
        </Flex>
      </HeaderSection>

      {/* Navigation */}
      <NavigationTabs
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />

      {/* Main Content */}
      <ContentSection>
        {renderTabContent()}
      </ContentSection>

      {/* Status Bar */}
      <StatusBar>
        <Text>{appState.processingStatus}</Text>
        <Text>
          Step {tabs.findIndex(tab => tab.id === appState.currentStep) + 1} of {tabs.length}
        </Text>
      </StatusBar>
    </AppContainer>
  );
};

export default App;
