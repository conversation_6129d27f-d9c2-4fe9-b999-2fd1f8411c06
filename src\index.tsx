import React from 'react';
import ReactDOM from 'react-dom/client';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider } from 'styled-components';

// Components
import App from './App';
import ErrorBoundary from './components/ErrorBoundary';

// Adobe Express SDK (loaded via CDN)
declare global {
  interface Window {
    addOnUISdk: any;
  }
}

// Styles
import './styles.css';

// Types
interface AppConfig {
  apiEndpoint: string;
  openaiApiKey: string;
  environment: 'development' | 'production';
  features: {
    aiOptimization: boolean;
    socialMediaIntegration: boolean;
    analytics: boolean;
    collaboration: boolean;
  };
}

// Configuration
const appConfig: AppConfig = {
  apiEndpoint: process.env.NODE_ENV === 'production' 
    ? 'https://api.brandflowpro.com' 
    : 'http://localhost:3001',
  openaiApiKey: process.env.REACT_APP_OPENAI_API_KEY || '',
  environment: process.env.NODE_ENV === 'production' ? 'production' : 'development',
  features: {
    aiOptimization: true,
    socialMediaIntegration: true,
    analytics: true,
    collaboration: false // Will be enabled post-hackathon
  }
};

// React Query client configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: 1,
      retryDelay: 1000,
    },
  },
});

// Styled Components theme
const styledTheme = {
  colors: {
    primary: '#0265DC',
    secondary: '#6B7280',
    success: '#059669',
    warning: '#D97706',
    error: '#DC2626',
    background: '#FFFFFF',
    surface: '#F9FAFB',
    text: '#111827',
    textSecondary: '#6B7280',
    border: '#E5E7EB',
  },
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    xxl: '48px',
  },
  borderRadius: {
    sm: '4px',
    md: '8px',
    lg: '12px',
    xl: '16px',
  },
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
  },
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
  },
};

// App initialization
async function initializeApp() {
  try {
    // Hide loading screen
    const loadingScreen = document.getElementById('loading-screen');
    if (loadingScreen) {
      loadingScreen.style.display = 'none';
    }

    // Wait for Adobe Add-on SDK to be ready
    if (window.addOnUISdk) {
      await window.addOnUISdk.ready;
      console.log('✅ Adobe Add-on SDK initialized successfully');
    } else {
      console.warn('⚠️ Adobe Add-on SDK not found - running in development mode');
    }

    // Log app configuration
    console.log('🚀 BrandFlow Pro initialized with config:', {
      environment: appConfig.environment,
      features: appConfig.features,
      apiEndpoint: appConfig.apiEndpoint,
    });

    return true;
  } catch (error) {
    console.error('❌ Failed to initialize BrandFlow Pro:', error);

    // Show error fallback
    const errorFallback = document.getElementById('error-fallback');
    if (errorFallback) {
      errorFallback.style.display = 'flex';
    }

    throw error;
  }
}

// Main App Component with Providers
const AppWithProviders: React.FC = () => {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider theme={styledTheme}>
          <App config={appConfig} />
        </ThemeProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

// Bootstrap the application
async function bootstrap() {
  try {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      await new Promise(resolve => {
        document.addEventListener('DOMContentLoaded', resolve);
      });
    }

    // Initialize the app
    await initializeApp();

    // Get root element
    const rootElement = document.getElementById('root');
    if (!rootElement) {
      throw new Error('Root element not found');
    }

    // Create React root and render app
    const root = ReactDOM.createRoot(rootElement);
    root.render(<AppWithProviders />);

    console.log('🎉 BrandFlow Pro successfully mounted to DOM');

  } catch (error) {
    console.error('💥 Failed to bootstrap BrandFlow Pro:', error);
    
    // Show error message
    const errorFallback = document.getElementById('error-fallback');
    const errorMessage = document.querySelector('.error-message');
    
    if (errorFallback) {
      errorFallback.style.display = 'flex';
    }
    
    if (errorMessage) {
      errorMessage.textContent = 'Failed to initialize the application. Please refresh and try again.';
    }
  }
}

// Start the application
bootstrap();

// Hot module replacement for development
if (process.env.NODE_ENV === 'development' && (module as any).hot) {
  (module as any).hot.accept('./App', () => {
    console.log('🔄 Hot reloading App component');
  });
}

// Export for testing
export { appConfig, queryClient, styledTheme };
