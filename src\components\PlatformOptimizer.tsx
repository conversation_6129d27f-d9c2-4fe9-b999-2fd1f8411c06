import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Flex,
  <PERSON>ing,
  Text,
  Button,
  Checkbox,
  ProgressBar,
  Well,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Switch,
  Slider
} from '@adobe/react-spectrum';
import styled from 'styled-components';

// Icons
import Import from '@spectrum-icons/workflow/Import';
import Refresh from '@spectrum-icons/workflow/Refresh';
import Settings from '@spectrum-icons/workflow/Settings';
import CheckmarkCircle from '@spectrum-icons/workflow/CheckmarkCircle';

// Services
import { optimizeForPlatform } from '../services/platformOptimization';
import { checkBrandCompliance } from '../services/brandAnalysis';

// Types
interface Platform {
  id: string;
  name: string;
  icon: string;
  specs: {
    aspectRatio: string;
    dimensions: { width: number; height: number };
    maxFileSize: string;
    formats: string[];
    textLimits?: {
      title: number;
      description: number;
    };
  };
  features: string[];
  selected: boolean;
}

interface BrandKit {
  id: string;
  name: string;
  colors: any;
  fonts: any;
  guidelines: any;
}

interface PlatformOptimizerProps {
  brandKit: BrandKit | null;
  currentDocument: any;
  onImportDesign: () => Promise<void>;
  onOptimizationComplete: () => void;
  isDesignImported: boolean;
}

// Styled Components
const PlatformGrid = styled(View)`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: ${props => props.theme.spacing.md};
  margin-top: ${props => props.theme.spacing.md};
`;

const PlatformCard = styled(View)<{ selected: boolean }>`
  background: white;
  border: 2px solid ${props => props.selected ? props.theme.colors.primary : props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.lg};
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    border-color: ${props => props.theme.colors.primary};
    box-shadow: ${props => props.theme.shadows.md};
  }
`;

const PlatformIcon = styled(View)`
  width: 48px;
  height: 48px;
  border-radius: ${props => props.theme.borderRadius.md};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-bottom: ${props => props.theme.spacing.sm};
`;

const ComplianceIndicator = styled(View)<{ status: 'compliant' | 'warning' | 'error' }>`
  position: absolute;
  top: ${props => props.theme.spacing.sm};
  right: ${props => props.theme.spacing.sm};
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: ${props => {
    switch (props.status) {
      case 'compliant': return props.theme.colors.success;
      case 'warning': return props.theme.colors.warning;
      case 'error': return props.theme.colors.error;
      default: return props.theme.colors.border;
    }
  }};
`;

const OptimizationSettings = styled(View)`
  background: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.md};
  padding: ${props => props.theme.spacing.lg};
  margin-top: ${props => props.theme.spacing.md};
`;

// Platform configurations
const PLATFORMS: Platform[] = [
  {
    id: 'instagram-post',
    name: 'Instagram Post',
    icon: '📷',
    specs: {
      aspectRatio: '1:1',
      dimensions: { width: 1080, height: 1080 },
      maxFileSize: '30MB',
      formats: ['JPG', 'PNG'],
      textLimits: { title: 125, description: 2200 }
    },
    features: ['Square format', 'High engagement', 'Visual focus'],
    selected: false
  },
  {
    id: 'instagram-story',
    name: 'Instagram Story',
    icon: '📱',
    specs: {
      aspectRatio: '9:16',
      dimensions: { width: 1080, height: 1920 },
      maxFileSize: '30MB',
      formats: ['JPG', 'PNG', 'MP4']
    },
    features: ['Vertical format', '24h visibility', 'Interactive elements'],
    selected: false
  },
  {
    id: 'facebook-post',
    name: 'Facebook Post',
    icon: '👥',
    specs: {
      aspectRatio: '16:9',
      dimensions: { width: 1200, height: 675 },
      maxFileSize: '25MB',
      formats: ['JPG', 'PNG']
    },
    features: ['Landscape format', 'Link sharing', 'Community engagement'],
    selected: false
  },
  {
    id: 'twitter-post',
    name: 'Twitter Post',
    icon: '🐦',
    specs: {
      aspectRatio: '16:9',
      dimensions: { width: 1200, height: 675 },
      maxFileSize: '5MB',
      formats: ['JPG', 'PNG', 'GIF'],
      textLimits: { title: 280, description: 0 }
    },
    features: ['Concise messaging', 'Real-time updates', 'Hashtag support'],
    selected: false
  },
  {
    id: 'linkedin-post',
    name: 'LinkedIn Post',
    icon: '💼',
    specs: {
      aspectRatio: '1.91:1',
      dimensions: { width: 1200, height: 627 },
      maxFileSize: '20MB',
      formats: ['JPG', 'PNG']
    },
    features: ['Professional network', 'B2B focus', 'Industry insights'],
    selected: false
  },
  {
    id: 'tiktok-video',
    name: 'TikTok Video',
    icon: '🎵',
    specs: {
      aspectRatio: '9:16',
      dimensions: { width: 1080, height: 1920 },
      maxFileSize: '287MB',
      formats: ['MP4', 'MOV']
    },
    features: ['Vertical video', 'Short-form content', 'Trending audio'],
    selected: false
  }
];

const PlatformOptimizer: React.FC<PlatformOptimizerProps> = ({
  brandKit,
  currentDocument,
  onImportDesign,
  onOptimizationComplete,
  isDesignImported
}) => {
  // State
  const [platforms, setPlatforms] = useState<Platform[]>(PLATFORMS);
  const [isImporting, setIsImporting] = useState(false);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [optimizationProgress, setOptimizationProgress] = useState(0);
  const [complianceResults, setComplianceResults] = useState<Record<string, any>>({});
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  const [optimizationSettings, setOptimizationSettings] = useState({
    maintainAspectRatio: true,
    preserveTextReadability: true,
    optimizeForMobile: true,
    compressionLevel: 80,
    colorProfileConversion: true,
    smartCropping: true
  });

  // Effects
  useEffect(() => {
    if (isDesignImported && brandKit) {
      checkBrandComplianceForPlatforms();
    }
  }, [isDesignImported, brandKit, platforms]);

  // Brand Compliance Check
  const checkBrandComplianceForPlatforms = async () => {
    const results: Record<string, any> = {};
    
    for (const platform of platforms.filter(p => p.selected)) {
      try {
        const compliance = await checkBrandCompliance(currentDocument, brandKit, platform);
        results[platform.id] = compliance;
      } catch (error) {
        console.error(`Compliance check failed for ${platform.name}:`, error);
        results[platform.id] = { status: 'error', issues: ['Compliance check failed'] };
      }
    }
    
    setComplianceResults(results);
  };

  // Platform Selection Handler
  const handlePlatformToggle = (platformId: string) => {
    setPlatforms(prev => prev.map(platform => 
      platform.id === platformId 
        ? { ...platform, selected: !platform.selected }
        : platform
    ));
  };

  // Import Design Handler
  const handleImportDesign = async () => {
    try {
      setIsImporting(true);
      await onImportDesign();
    } catch (error) {
      console.error('Failed to import design:', error);
    } finally {
      setIsImporting(false);
    }
  };

  // Optimization Handler
  const handleOptimization = async () => {
    const selectedPlatforms = platforms.filter(p => p.selected);
    
    if (selectedPlatforms.length === 0) {
      alert('Please select at least one platform to optimize for.');
      return;
    }

    try {
      setIsOptimizing(true);
      setOptimizationProgress(0);

      // Simulate optimization progress
      const progressInterval = setInterval(() => {
        setOptimizationProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 300);

      // Optimize for each selected platform
      for (let i = 0; i < selectedPlatforms.length; i++) {
        const platform = selectedPlatforms[i];
        await optimizeForPlatform(currentDocument, platform, brandKit, optimizationSettings);
        
        // Update progress
        const progress = ((i + 1) / selectedPlatforms.length) * 100;
        setOptimizationProgress(progress);
      }

      clearInterval(progressInterval);
      setOptimizationProgress(100);
      
      // Complete optimization
      setTimeout(() => {
        onOptimizationComplete();
        setIsOptimizing(false);
        setOptimizationProgress(0);
      }, 1000);

    } catch (error) {
      console.error('Optimization failed:', error);
      setIsOptimizing(false);
      setOptimizationProgress(0);
    }
  };

  // Render Import Section
  const renderImportSection = () => (
    <View>
      <Heading level={3}>Import Design</Heading>
      <Text>Import your current Adobe Express design to start optimization.</Text>
      
      <Flex gap="size-200" marginTop="size-200">
        <Button 
          variant="cta" 
          onPress={handleImportDesign}
          isDisabled={isImporting || isDesignImported}
        >
          <Import />
          <Text>{isImporting ? 'Importing...' : isDesignImported ? 'Design Imported' : 'Import Current Design'}</Text>
        </Button>
        
        {isDesignImported && (
          <ActionButton onPress={handleImportDesign}>
            <Refresh />
            <Text>Refresh</Text>
          </ActionButton>
        )}
      </Flex>

      {isImporting && (
        <ProgressBar
          label="Importing design elements..."
          isIndeterminate
          marginTop="size-200"
        />
      )}

      {isDesignImported && (
        <Well marginTop="size-200">
          <Flex alignItems="center" gap="size-100">
            <CheckmarkCircle color="positive" />
            <Text>Design imported successfully! Select platforms below to optimize.</Text>
          </Flex>
        </Well>
      )}
    </View>
  );

  // Render Platform Selection
  const renderPlatformSelection = () => (
    <View>
      <Flex justifyContent="space-between" alignItems="center">
        <Heading level={3}>Select Target Platforms</Heading>
        <ActionButton 
          onPress={() => setShowAdvancedSettings(!showAdvancedSettings)}
          isQuiet
        >
          <Settings />
          <Text>Advanced Settings</Text>
        </ActionButton>
      </Flex>
      
      <Text>Choose the social media platforms you want to optimize your design for.</Text>

      <PlatformGrid>
        {platforms.map(platform => (
          <PlatformCard
            key={platform.id}
            selected={platform.selected}
            onClick={() => handlePlatformToggle(platform.id)}
          >
            <ComplianceIndicator 
              status={complianceResults[platform.id]?.status || 'compliant'} 
            />
            
            <Flex direction="column" gap="size-100">
              <Flex alignItems="center" gap="size-200">
                <PlatformIcon>{platform.icon}</PlatformIcon>
                <View>
                  <Heading level={4}>{platform.name}</Heading>
                  <Text>{platform.specs.aspectRatio} • {platform.specs.dimensions.width}×{platform.specs.dimensions.height}</Text>
                </View>
              </Flex>

              <Text slot="description">
                {platform.features.join(' • ')}
              </Text>

              <Flex justifyContent="space-between" alignItems="center">
                <Text>Max: {platform.specs.maxFileSize}</Text>
                <Checkbox 
                  isSelected={platform.selected}
                  onChange={() => handlePlatformToggle(platform.id)}
                >
                  Select
                </Checkbox>
              </Flex>
            </Flex>
          </PlatformCard>
        ))}
      </PlatformGrid>
    </View>
  );

  // Render Advanced Settings
  const renderAdvancedSettings = () => {
    if (!showAdvancedSettings) return null;

    return (
      <OptimizationSettings>
        <Heading level={4}>Optimization Settings</Heading>
        
        <Flex direction="column" gap="size-200">
          <Switch 
            isSelected={optimizationSettings.maintainAspectRatio}
            onChange={(value) => setOptimizationSettings(prev => ({ ...prev, maintainAspectRatio: value }))}
          >
            Maintain aspect ratio when possible
          </Switch>
          
          <Switch 
            isSelected={optimizationSettings.preserveTextReadability}
            onChange={(value) => setOptimizationSettings(prev => ({ ...prev, preserveTextReadability: value }))}
          >
            Preserve text readability
          </Switch>
          
          <Switch 
            isSelected={optimizationSettings.optimizeForMobile}
            onChange={(value) => setOptimizationSettings(prev => ({ ...prev, optimizeForMobile: value }))}
          >
            Optimize for mobile viewing
          </Switch>
          
          <Switch 
            isSelected={optimizationSettings.smartCropping}
            onChange={(value) => setOptimizationSettings(prev => ({ ...prev, smartCropping: value }))}
          >
            Enable AI-powered smart cropping
          </Switch>

          <View>
            <Text>Compression Level: {optimizationSettings.compressionLevel}%</Text>
            <Slider
              value={optimizationSettings.compressionLevel}
              onChange={(value) => setOptimizationSettings(prev => ({ ...prev, compressionLevel: value }))}
              minValue={10}
              maxValue={100}
              step={10}
            />
          </View>
        </Flex>
      </OptimizationSettings>
    );
  };

  // Render Optimization Section
  const renderOptimizationSection = () => {
    const selectedCount = platforms.filter(p => p.selected).length;
    
    return (
      <View>
        <Flex justifyContent="space-between" alignItems="center">
          <Heading level={3}>Start Optimization</Heading>
          <Text>{selectedCount} platform{selectedCount !== 1 ? 's' : ''} selected</Text>
        </Flex>

        <Button 
          variant="cta" 
          onPress={handleOptimization}
          isDisabled={!isDesignImported || selectedCount === 0 || isOptimizing}
          width="100%"
          marginTop="size-200"
        >
          <Text>
            {isOptimizing ? 'Optimizing...' : `Optimize for ${selectedCount} Platform${selectedCount !== 1 ? 's' : ''}`}
          </Text>
        </Button>

        {isOptimizing && (
          <ProgressBar
            label="Optimizing design for selected platforms..."
            value={optimizationProgress}
            maxValue={100}
            showValueLabel
            marginTop="size-200"
          />
        )}
      </View>
    );
  };

  return (
    <View padding="size-200">
      <Flex direction="column" gap="size-400">
        <View>
          <Heading level={2}>Platform Optimization</Heading>
          <Text>
            Optimize your design for multiple social media platforms while maintaining brand consistency.
          </Text>
        </View>

        {renderImportSection()}
        
        {isDesignImported && (
          <>
            {renderPlatformSelection()}
            {renderAdvancedSettings()}
            {renderOptimizationSection()}
          </>
        )}
      </Flex>
    </View>
  );
};

export default PlatformOptimizer;
