/**
 * BrandFlow Pro - Brand Analysis Service
 * AI-powered brand analysis using OpenAI GPT-4 Vision API
 * and custom algorithms for brand guideline extraction
 */

import OpenAI from 'openai';
import { BrandKit, BrandAnalysisResult, ComplianceResult } from '../types/brand';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.REACT_APP_OPENAI_API_KEY,
  dangerouslyAllowBrowser: true // Note: In production, use a backend proxy
});

/**
 * Analyze brand assets using AI to extract brand guidelines
 */
export async function analyzeBrandAssets(files: FileList): Promise<BrandAnalysisResult> {
  try {
    console.log('🤖 Starting AI brand analysis...');
    
    const analysisResults: BrandAnalysisResult = {
      colors: {
        primary: '#0265DC',
        secondary: '#6B7280',
        accent: '#059669',
        neutral: ['#FFFFFF', '#F9FAFB', '#E5E7EB', '#111827']
      },
      fonts: {
        primary: 'Inter',
        secondary: 'Arial',
        weights: ['400', '500', '600', '700']
      },
      guidelines: {
        spacing: 16,
        borderRadius: 8,
        minLogoSize: 32,
        clearSpace: 24
      },
      confidence: 0.85,
      suggestions: []
    };

    // Process each file
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      if (file.type.startsWith('image/')) {
        const imageAnalysis = await analyzeImageAsset(file);
        mergeAnalysisResults(analysisResults, imageAnalysis);
      } else if (file.type === 'application/pdf') {
        const pdfAnalysis = await analyzePDFAsset(file);
        mergeAnalysisResults(analysisResults, pdfAnalysis);
      }
    }

    // Refine results using AI
    const refinedResults = await refineWithAI(analysisResults, files);
    
    console.log('✅ Brand analysis completed:', refinedResults);
    return refinedResults;

  } catch (error) {
    console.error('❌ Brand analysis failed:', error);
    throw new Error('Failed to analyze brand assets. Please try again.');
  }
}

/**
 * Analyze individual image asset
 */
async function analyzeImageAsset(file: File): Promise<Partial<BrandAnalysisResult>> {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      ctx?.drawImage(img, 0, 0);
      
      // Extract dominant colors
      const colors = extractDominantColors(canvas);
      
      // Analyze typography (if text is present)
      const fonts = analyzeTypography(canvas);
      
      resolve({
        colors: {
          primary: colors[0] || '#0265DC',
          secondary: colors[1] || '#6B7280',
          accent: colors[2] || '#059669',
          neutral: colors.slice(3, 7)
        },
        fonts,
        confidence: 0.7
      });
    };
    
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Extract dominant colors from canvas using color quantization
 */
function extractDominantColors(canvas: HTMLCanvasElement): string[] {
  const ctx = canvas.getContext('2d');
  if (!ctx) return [];
  
  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const pixels = imageData.data;
  const colorMap = new Map<string, number>();
  
  // Sample pixels (every 10th pixel for performance)
  for (let i = 0; i < pixels.length; i += 40) {
    const r = pixels[i];
    const g = pixels[i + 1];
    const b = pixels[i + 2];
    const alpha = pixels[i + 3];
    
    // Skip transparent pixels
    if (alpha < 128) continue;
    
    // Quantize colors to reduce noise
    const quantizedR = Math.round(r / 32) * 32;
    const quantizedG = Math.round(g / 32) * 32;
    const quantizedB = Math.round(b / 32) * 32;
    
    const colorKey = `${quantizedR},${quantizedG},${quantizedB}`;
    colorMap.set(colorKey, (colorMap.get(colorKey) || 0) + 1);
  }
  
  // Sort colors by frequency and convert to hex
  const sortedColors = Array.from(colorMap.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 8)
    .map(([color]) => {
      const [r, g, b] = color.split(',').map(Number);
      return rgbToHex(r, g, b);
    });
  
  return sortedColors;
}

/**
 * Analyze typography from image (basic implementation)
 */
function analyzeTypography(canvas: HTMLCanvasElement): { primary: string; secondary: string; weights: string[] } {
  // This is a simplified implementation
  // In a real-world scenario, you'd use OCR and font detection
  return {
    primary: 'Inter',
    secondary: 'Arial',
    weights: ['400', '500', '600']
  };
}

/**
 * Analyze PDF asset for brand guidelines
 */
async function analyzePDFAsset(file: File): Promise<Partial<BrandAnalysisResult>> {
  // This would require a PDF parsing library like PDF.js
  // For now, return default values
  return {
    guidelines: {
      spacing: 16,
      borderRadius: 8,
      minLogoSize: 32,
      clearSpace: 24
    },
    confidence: 0.6
  };
}

/**
 * Refine analysis results using OpenAI GPT-4 Vision
 */
async function refineWithAI(
  initialResults: BrandAnalysisResult, 
  files: FileList
): Promise<BrandAnalysisResult> {
  try {
    // Convert first image to base64 for AI analysis
    const firstImageFile = Array.from(files).find(f => f.type.startsWith('image/'));
    if (!firstImageFile) return initialResults;
    
    const base64Image = await fileToBase64(firstImageFile);
    
    const response = await openai.chat.completions.create({
      model: "gpt-4-vision-preview",
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `Analyze this brand asset and provide brand guidelines. Current analysis: ${JSON.stringify(initialResults)}. Please refine the color palette, suggest typography improvements, and provide spacing recommendations. Respond in JSON format with the same structure.`
            },
            {
              type: "image_url",
              image_url: {
                url: base64Image
              }
            }
          ]
        }
      ],
      max_tokens: 1000
    });
    
    const aiResponse = response.choices[0]?.message?.content;
    if (aiResponse) {
      try {
        const refinedResults = JSON.parse(aiResponse);
        return {
          ...initialResults,
          ...refinedResults,
          confidence: Math.min(initialResults.confidence + 0.1, 0.95),
          suggestions: [
            ...initialResults.suggestions,
            'AI-refined color palette for better brand consistency',
            'Typography recommendations based on visual analysis'
          ]
        };
      } catch (parseError) {
        console.warn('Failed to parse AI response, using initial results');
      }
    }
    
    return initialResults;
    
  } catch (error) {
    console.warn('AI refinement failed, using initial results:', error);
    return initialResults;
  }
}

/**
 * Check brand compliance for a document against brand kit
 */
export async function checkBrandCompliance(
  document: any,
  brandKit: BrandKit,
  platform?: any
): Promise<ComplianceResult> {
  try {
    const complianceResult: ComplianceResult = {
      overall: 'compliant',
      score: 100,
      issues: [],
      suggestions: [],
      platformSpecific: platform ? [] : undefined
    };
    
    // Check color compliance
    const colorIssues = checkColorCompliance(document, brandKit);
    complianceResult.issues.push(...colorIssues);
    
    // Check typography compliance
    const typographyIssues = checkTypographyCompliance(document, brandKit);
    complianceResult.issues.push(...typographyIssues);
    
    // Check spacing compliance
    const spacingIssues = checkSpacingCompliance(document, brandKit);
    complianceResult.issues.push(...spacingIssues);
    
    // Platform-specific checks
    if (platform) {
      const platformIssues = checkPlatformCompliance(document, platform);
      complianceResult.platformSpecific = platformIssues;
    }
    
    // Calculate overall score and status
    const totalIssues = complianceResult.issues.length;
    if (totalIssues === 0) {
      complianceResult.overall = 'compliant';
      complianceResult.score = 100;
    } else if (totalIssues <= 2) {
      complianceResult.overall = 'warning';
      complianceResult.score = Math.max(70, 100 - (totalIssues * 15));
    } else {
      complianceResult.overall = 'error';
      complianceResult.score = Math.max(30, 100 - (totalIssues * 20));
    }
    
    // Generate suggestions
    if (complianceResult.issues.length > 0) {
      complianceResult.suggestions = generateComplianceSuggestions(complianceResult.issues, brandKit);
    }
    
    return complianceResult;
    
  } catch (error) {
    console.error('Brand compliance check failed:', error);
    return {
      overall: 'error',
      score: 0,
      issues: [{ type: 'system', message: 'Compliance check failed', severity: 'high' }],
      suggestions: ['Please try running the compliance check again']
    };
  }
}

/**
 * Check color compliance
 */
function checkColorCompliance(document: any, brandKit: BrandKit): any[] {
  const issues = [];
  const brandColors = [
    brandKit.colors.primary,
    brandKit.colors.secondary,
    brandKit.colors.accent,
    ...brandKit.colors.neutral
  ];
  
  // This would analyze document colors against brand colors
  // Simplified implementation for demo
  
  return issues;
}

/**
 * Check typography compliance
 */
function checkTypographyCompliance(document: any, brandKit: BrandKit): any[] {
  const issues = [];
  const brandFonts = [brandKit.fonts.primary, brandKit.fonts.secondary];
  
  // This would analyze document fonts against brand fonts
  // Simplified implementation for demo
  
  return issues;
}

/**
 * Check spacing compliance
 */
function checkSpacingCompliance(document: any, brandKit: BrandKit): any[] {
  const issues = [];
  
  // This would analyze element spacing against brand guidelines
  // Simplified implementation for demo
  
  return issues;
}

/**
 * Check platform-specific compliance
 */
function checkPlatformCompliance(document: any, platform: any): any[] {
  const issues = [];
  
  // Check dimensions
  if (document.width !== platform.specs.dimensions.width ||
      document.height !== platform.specs.dimensions.height) {
    issues.push({
      type: 'dimensions',
      message: `Document size (${document.width}x${document.height}) doesn't match platform requirements (${platform.specs.dimensions.width}x${platform.specs.dimensions.height})`,
      severity: 'medium'
    });
  }
  
  return issues;
}

/**
 * Generate compliance suggestions
 */
function generateComplianceSuggestions(issues: any[], brandKit: BrandKit): string[] {
  const suggestions = [];
  
  const colorIssues = issues.filter(i => i.type === 'color');
  if (colorIssues.length > 0) {
    suggestions.push(`Use brand colors: ${brandKit.colors.primary}, ${brandKit.colors.secondary}`);
  }
  
  const typographyIssues = issues.filter(i => i.type === 'typography');
  if (typographyIssues.length > 0) {
    suggestions.push(`Use brand fonts: ${brandKit.fonts.primary} or ${brandKit.fonts.secondary}`);
  }
  
  const spacingIssues = issues.filter(i => i.type === 'spacing');
  if (spacingIssues.length > 0) {
    suggestions.push(`Maintain consistent spacing of ${brandKit.guidelines.spacing}px`);
  }
  
  return suggestions;
}

/**
 * Merge analysis results
 */
function mergeAnalysisResults(target: BrandAnalysisResult, source: Partial<BrandAnalysisResult>): void {
  if (source.colors) {
    target.colors = { ...target.colors, ...source.colors };
  }
  if (source.fonts) {
    target.fonts = { ...target.fonts, ...source.fonts };
  }
  if (source.guidelines) {
    target.guidelines = { ...target.guidelines, ...source.guidelines };
  }
  if (source.suggestions) {
    target.suggestions.push(...source.suggestions);
  }
}

/**
 * Utility functions
 */
function rgbToHex(r: number, g: number, b: number): string {
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
}

async function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}
