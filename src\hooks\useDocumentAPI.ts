import { useState, useEffect, useCallback } from 'react';
import { DocumentElement } from '../types/brand';

interface UseDocumentAPIReturn {
  currentDocument: any;
  documentElements: DocumentElement[];
  loading: boolean;
  error: string | null;
  importDesign: () => Promise<void>;
  refreshElements: () => Promise<void>;
}

export const useDocumentAPI = (): UseDocumentAPIReturn => {
  const [currentDocument, setCurrentDocument] = useState<any>(null);
  const [documentElements, setDocumentElements] = useState<DocumentElement[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize document API connection
  useEffect(() => {
    const initializeDocumentAPI = async () => {
      try {
        if (window.addOnUISdk) {
          await window.addOnUISdk.ready;
          
          // Send message to document sandbox to initialize
          window.addOnUISdk.app.document.postMessage({
            type: 'INIT_DOCUMENT_API'
          });
          
          // Listen for document ready response
          window.addOnUISdk.app.document.on('message', (message: any) => {
            const { type, payload } = message;
            
            switch (type) {
              case 'DOCUMENT_READY':
                setCurrentDocument({ id: payload.documentId });
                console.log('Document API ready');
                break;
                
              case 'DOCUMENT_ELEMENTS':
                setDocumentElements(payload.elements);
                break;
                
              case 'ERROR':
                setError(payload.message);
                break;
            }
          });
        }
      } catch (err) {
        console.error('Failed to initialize document API:', err);
        setError('Failed to connect to Adobe Express document');
      }
    };

    initializeDocumentAPI();
  }, []);

  const importDesign = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      if (window.addOnUISdk) {
        // Request document elements from sandbox
        window.addOnUISdk.app.document.postMessage({
          type: 'GET_DOCUMENT_ELEMENTS'
        });
      } else {
        // Mock data for development
        const mockElements: DocumentElement[] = [
          {
            id: 'text-1',
            type: 'text',
            bounds: { x: 100, y: 100, width: 200, height: 50 },
            visible: true,
            locked: false,
            properties: {
              text: 'Sample Text',
              fontSize: 24,
              fontFamily: 'Arial',
              color: '#000000'
            }
          },
          {
            id: 'image-1',
            type: 'image',
            bounds: { x: 50, y: 200, width: 300, height: 200 },
            visible: true,
            locked: false,
            properties: {
              src: 'https://via.placeholder.com/300x200',
              naturalWidth: 300,
              naturalHeight: 200
            }
          }
        ];
        
        setDocumentElements(mockElements);
        setCurrentDocument({ id: 'mock-document' });
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to import design');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshElements = useCallback(async () => {
    if (window.addOnUISdk) {
      window.addOnUISdk.app.document.postMessage({
        type: 'GET_DOCUMENT_ELEMENTS'
      });
    }
  }, []);

  return {
    currentDocument,
    documentElements,
    loading,
    error,
    importDesign,
    refreshElements,
  };
};
