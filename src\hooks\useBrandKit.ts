import { useState, useCallback } from 'react';
import { BrandKit, ComplianceResult } from '../types/brand';

interface UseBrandKitReturn {
  brandKit: BrandKit | null;
  loading: boolean;
  error: string | null;
  uploadBrandKit: (files: FileList) => Promise<void>;
  updateBrandKit: (updates: Partial<BrandKit>) => Promise<void>;
  deleteBrandKit: (id: string) => Promise<void>;
  validateBrandKit: (brandKit: BrandKit) => ComplianceResult;
}

export const useBrandKit = (): UseBrandKitReturn => {
  const [brandKit, setBrandKit] = useState<BrandKit | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const uploadBrandKit = useCallback(async (files: FileList) => {
    setLoading(true);
    setError(null);
    
    try {
      const formData = new FormData();
      Array.from(files).forEach((file, index) => {
        formData.append('assets', file);
      });

      const response = await fetch('/api/upload/brand-kit', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to upload brand kit');
      }

      const result = await response.json();
      setBrandKit(result.brandKit);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Upload failed');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateBrandKit = useCallback(async (updates: Partial<BrandKit>) => {
    if (!brandKit) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/brand-kit/${brandKit.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        throw new Error('Failed to update brand kit');
      }

      const result = await response.json();
      setBrandKit(result.brandKit);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Update failed');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [brandKit]);

  const deleteBrandKit = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/brand-kit/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete brand kit');
      }

      setBrandKit(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Delete failed');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const validateBrandKit = useCallback((brandKit: BrandKit): ComplianceResult => {
    const issues = [];
    
    // Basic validation
    if (!brandKit.colors.primary) {
      issues.push({
        type: 'color' as const,
        message: 'Primary color is required',
        severity: 'high' as const
      });
    }
    
    if (!brandKit.fonts.primary) {
      issues.push({
        type: 'typography' as const,
        message: 'Primary font is required',
        severity: 'high' as const
      });
    }

    return {
      overall: issues.length === 0 ? 'compliant' : 'warning',
      score: Math.max(0, 100 - (issues.length * 20)),
      issues,
      suggestions: issues.length > 0 ? ['Complete all required brand elements'] : []
    };
  }, []);

  return {
    brandKit,
    loading,
    error,
    uploadBrandKit,
    updateBrandKit,
    deleteBrandKit,
    validateBrandKit,
  };
};
