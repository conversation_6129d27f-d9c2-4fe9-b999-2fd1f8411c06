const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');

module.exports = (env, argv) => {
  const isProduction = argv.mode === 'production';
  
  return {
    entry: {
      main: './src/index.tsx',
      documentSandbox: './src/sandbox/documentAPI.js'
    },
    
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: '[name].js',
      clean: true,
    },
    
    resolve: {
      extensions: ['.tsx', '.ts', '.js', '.jsx'],
    },
    
    module: {
      rules: [
        {
          test: /\.tsx?$/,
          use: 'ts-loader',
          exclude: /node_modules/,
        },
        {
          test: /\.css$/i,
          use: ['style-loader', 'css-loader'],
        },
        {
          test: /\.(png|svg|jpg|jpeg|gif)$/i,
          type: 'asset/resource',
        },
      ],
    },
    
    plugins: [
      new HtmlWebpackPlugin({
        template: './src/index.html',
        filename: 'index.html',
        chunks: ['main'],
      }),
    ],
    
    devServer: {
      static: {
        directory: path.join(__dirname, 'dist'),
      },
      port: 5241,
      https: true,
      hot: true,
      open: false,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
        'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization',
      },
    },
    
    externals: {
      // Adobe Express Add-on SDK is loaded via CDN
      'https://new.express.adobe.com/static/add-on-sdk/sdk.js': 'addOnUISdk',
      'add-on-sdk-document-sandbox': 'addOnSandboxSdk',
      'express-document-sdk': 'expressDocumentSdk',
    },
    
    optimization: {
      splitChunks: {
        chunks: 'all',
      },
    },
    
    devtool: isProduction ? 'source-map' : 'eval-source-map',
  };
};
