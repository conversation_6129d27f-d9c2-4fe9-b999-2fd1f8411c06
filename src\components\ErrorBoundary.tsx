import React, { Component, ErrorInfo, ReactNode } from 'react';
import { View, Flex, Heading, Text, Button } from '@adobe/react-spectrum';
import styled from 'styled-components';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

const ErrorContainer = styled(View)`
  padding: ${props => props.theme.spacing.xl};
  text-align: center;
  background: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.lg};
  margin: ${props => props.theme.spacing.lg};
`;

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({ error, errorInfo });
  }

  private handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  public render() {
    if (this.state.hasError) {
      return (
        <ErrorContainer>
          <Flex direction="column" alignItems="center" gap="size-200">
            <div style={{ fontSize: '48px' }}>⚠️</div>
            <Heading level={2}>Something went wrong</Heading>
            <Text>
              BrandFlow Pro encountered an unexpected error. Please try refreshing the add-on.
            </Text>
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <View
                backgroundColor="gray-100"
                padding="size-200"
                borderRadius="medium"
                width="100%"
                maxWidth="600px"
              >
                <Text>
                  <strong>Error:</strong> {this.state.error.message}
                </Text>
                {this.state.errorInfo && (
                  <Text>
                    <strong>Stack:</strong>
                    <pre style={{ fontSize: '12px', overflow: 'auto' }}>
                      {this.state.errorInfo.componentStack}
                    </pre>
                  </Text>
                )}
              </View>
            )}
            <Button variant="cta" onPress={this.handleRetry}>
              Try Again
            </Button>
          </Flex>
        </ErrorContainer>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
