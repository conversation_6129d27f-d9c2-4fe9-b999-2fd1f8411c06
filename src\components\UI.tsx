/**
 * Custom UI Components
 * Replacement for Adobe Spectrum components with styled-components
 */

import React from 'react';
import styled from 'styled-components';

// Basic Layout Components
export const View = styled.div`
  display: block;
`;

export const Flex = styled.div<{ 
  direction?: string; 
  gap?: string; 
  justifyContent?: string; 
  alignItems?: string;
  wrap?: string;
}>`
  display: flex;
  flex-direction: ${props => props.direction || 'row'};
  gap: ${props => props.gap || '0'};
  justify-content: ${props => props.justifyContent || 'flex-start'};
  align-items: ${props => props.alignItems || 'stretch'};
  flex-wrap: ${props => props.wrap || 'nowrap'};
`;

// Typography Components
export const Heading = styled.h2<{ level?: number }>`
  margin: 0;
  font-size: ${props => {
    switch (props.level) {
      case 1: return '2rem';
      case 2: return '1.5rem';
      case 3: return '1.25rem';
      case 4: return '1.125rem';
      default: return '1.5rem';
    }
  }};
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  line-height: 1.2;
`;

export const Text = styled.p<{ color?: string; size?: string }>`
  margin: 0;
  color: ${props => {
    if (props.color === 'positive') return props.theme.colors.success;
    if (props.color === 'negative') return props.theme.colors.error;
    return props.theme.colors.text;
  }};
  font-size: ${props => {
    switch (props.size) {
      case 'small': return '0.875rem';
      case 'large': return '1.125rem';
      default: return '1rem';
    }
  }};
  line-height: 1.5;
`;

// Button Components
export const Button = styled.button<{ variant?: string; size?: string; disabled?: boolean }>`
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: ${props => {
    switch (props.size) {
      case 'small': return '6px 12px';
      case 'large': return '12px 24px';
      default: return '8px 16px';
    }
  }};
  border: 1px solid;
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: ${props => props.theme.transition};
  text-decoration: none;
  
  ${props => {
    switch (props.variant) {
      case 'cta':
        return `
          background: ${props.theme.colors.primary};
          border-color: ${props.theme.colors.primary};
          color: white;
          
          &:hover:not(:disabled) {
            background: ${props.theme.colors.primary}dd;
            border-color: ${props.theme.colors.primary}dd;
          }
        `;
      case 'secondary':
        return `
          background: transparent;
          border-color: ${props.theme.colors.border};
          color: ${props.theme.colors.text};
          
          &:hover:not(:disabled) {
            background: ${props.theme.colors.surface};
            border-color: ${props.theme.colors.primary};
          }
        `;
      case 'negative':
        return `
          background: ${props.theme.colors.error};
          border-color: ${props.theme.colors.error};
          color: white;
          
          &:hover:not(:disabled) {
            background: ${props.theme.colors.error}dd;
            border-color: ${props.theme.colors.error}dd;
          }
        `;
      default:
        return `
          background: ${props.theme.colors.surface};
          border-color: ${props.theme.colors.border};
          color: ${props.theme.colors.text};
          
          &:hover:not(:disabled) {
            background: ${props.theme.colors.background};
            border-color: ${props.theme.colors.primary};
          }
        `;
    }
  }}
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

export const ActionButton = styled.button<{ disabled?: boolean }>`
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 6px 8px;
  background: transparent;
  border: 1px solid transparent;
  border-radius: ${props => props.theme.borderRadius.sm};
  color: ${props => props.theme.colors.text};
  font-size: 12px;
  cursor: pointer;
  transition: ${props => props.theme.transition};
  
  &:hover:not(:disabled) {
    background: ${props => props.theme.colors.surface};
    border-color: ${props => props.theme.colors.border};
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

// Form Components
export const TextField = styled.input<{ label?: string }>`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: 14px;
  color: ${props => props.theme.colors.text};
  background: white;
  transition: ${props => props.theme.transition};
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;
  }
  
  &::placeholder {
    color: ${props => props.theme.colors.textSecondary};
  }
`;

export const ColorField = styled.input.attrs({ type: 'color' })`
  width: 60px;
  height: 40px;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  cursor: pointer;
  background: none;
  
  &::-webkit-color-swatch-wrapper {
    padding: 0;
  }
  
  &::-webkit-color-swatch {
    border: none;
    border-radius: ${props => props.theme.borderRadius.sm};
  }
`;

export const Checkbox = styled.input.attrs({ type: 'checkbox' })`
  width: 16px;
  height: 16px;
  accent-color: ${props => props.theme.colors.primary};
  cursor: pointer;
`;

export const Switch = styled.label<{ isSelected?: boolean }>`
  display: inline-flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  
  input {
    width: 40px;
    height: 20px;
    appearance: none;
    background: ${props => props.isSelected ? props.theme.colors.primary : props.theme.colors.border};
    border-radius: 10px;
    position: relative;
    cursor: pointer;
    transition: ${props => props.theme.transition};
    
    &::before {
      content: '';
      position: absolute;
      top: 2px;
      left: ${props => props.isSelected ? '22px' : '2px'};
      width: 16px;
      height: 16px;
      background: white;
      border-radius: 50%;
      transition: ${props => props.theme.transition};
    }
  }
`;

// Progress Components
export const ProgressBar = styled.div<{ 
  value: number; 
  maxValue: number; 
  showValueLabel?: boolean;
  label?: string;
}>`
  width: 100%;
  
  .progress-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
    font-size: 12px;
    color: ${props => props.theme.colors.textSecondary};
  }
  
  .progress-track {
    width: 100%;
    height: 8px;
    background: ${props => props.theme.colors.border};
    border-radius: 4px;
    overflow: hidden;
  }
  
  .progress-fill {
    height: 100%;
    width: ${props => (props.value / props.maxValue) * 100}%;
    background: ${props => props.theme.colors.primary};
    transition: width 0.3s ease;
  }
`;

// Well Component
export const Well = styled.div`
  padding: 16px;
  background: ${props => props.theme.colors.surface};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
`;

// Picker Component
export const Picker = styled.select<{ label?: string }>`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: 14px;
  color: ${props => props.theme.colors.text};
  background: white;
  cursor: pointer;
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;
  }
`;

export const Item = styled.option`
  padding: 8px;
`;

// Slider Component
export const Slider = styled.input.attrs({ type: 'range' })`
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: ${props => props.theme.colors.border};
  outline: none;
  appearance: none;
  
  &::-webkit-slider-thumb {
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: ${props => props.theme.colors.primary};
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  }
  
  &::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: ${props => props.theme.colors.primary};
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  }
`;

// Progress Bar Component with proper structure
interface ProgressBarProps {
  value: number;
  maxValue: number;
  showValueLabel?: boolean;
  label?: string;
  className?: string;
}

export const ProgressBarComponent: React.FC<ProgressBarProps> = ({
  value,
  maxValue,
  showValueLabel,
  label,
  className
}) => {
  const percentage = Math.round((value / maxValue) * 100);
  
  return (
    <ProgressBar value={value} maxValue={maxValue} className={className}>
      {(label || showValueLabel) && (
        <div className="progress-label">
          {label && <span>{label}</span>}
          {showValueLabel && <span>{percentage}%</span>}
        </div>
      )}
      <div className="progress-track">
        <div className="progress-fill" />
      </div>
    </ProgressBar>
  );
};
