{"name": "brandflow-pro", "version": "1.0.0", "description": "AI-powered brand consistency and multi-platform content optimization for Adobe Express", "main": "src/index.tsx", "scripts": {"start": "webpack serve --mode development", "build": "webpack --mode production", "clean": "rm -rf dist", "package": "npm run build && zip -r brandflow-pro.zip dist manifest.json", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "type-check": "tsc --noEmit", "dev:server": "nodemon server/app.js", "server:start": "node server/app.js", "server:dev": "nodemon server/app.js"}, "keywords": ["adobe-express", "add-on", "brand-consistency", "social-media", "ai", "automation", "content-optimization", "workflow", "marketing"], "author": {"name": "HectorTa1989", "url": "https://github.com/HectorTa1989"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/brandflow-pro.git"}, "bugs": {"url": "https://github.com/HectorTa1989/brandflow-pro/issues"}, "homepage": "https://github.com/HectorTa1989/brandflow-pro#readme", "dependencies": {"@adobe/react-spectrum": "^3.34.1", "@adobe/spectrum-web-components": "^0.42.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "react": "^18.2.0", "react-dom": "^18.2.0", "react-query": "^3.39.3", "styled-components": "^6.1.6", "axios": "^1.6.2", "openai": "^4.24.1", "sharp": "^0.33.1", "canvas": "^2.11.2", "color": "^4.2.3", "lodash": "^4.17.21", "uuid": "^9.0.1", "date-fns": "^3.0.6"}, "devDependencies": {"webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "html-webpack-plugin": "^5.5.4", "css-loader": "^6.8.1", "style-loader": "^3.3.3", "ts-loader": "^9.5.1", "@types/node": "^20.10.5", "@types/lodash": "^4.14.202", "@types/uuid": "^9.0.7", "@types/color": "^3.0.6", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.6", "typescript": "^5.3.3", "nodemon": "^3.0.2", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "mongoose": "^8.0.3", "redis": "^4.6.12", "multer": "^1.4.5-lts.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "joi": "^17.11.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/src/setupTests.ts"], "moduleNameMapping": {"\\.(css|less|scss|sass)$": "identity-obj-proxy"}, "collectCoverageFrom": ["src/**/*.{ts,tsx}", "!src/**/*.d.ts", "!src/index.tsx"]}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}}