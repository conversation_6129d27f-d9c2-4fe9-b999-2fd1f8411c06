import { useCallback } from 'react';
import { AnalyticsEvent, PerformanceMetrics, UsageStatistics } from '../types/brand';

interface UseAnalyticsReturn {
  trackEvent: (event: string, properties?: Record<string, any>) => void;
  getMetrics: () => PerformanceMetrics;
  getUsageStats: () => UsageStatistics;
}

export const useAnalytics = (): UseAnalyticsReturn => {
  const trackEvent = useCallback((event: string, properties: Record<string, any> = {}) => {
    try {
      const analyticsEvent: AnalyticsEvent = {
        type: event,
        properties: {
          ...properties,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          url: window.location.href,
        },
        timestamp: new Date(),
        sessionId: getSessionId(),
      };

      // Send to analytics service
      if (process.env.NODE_ENV === 'production') {
        fetch('/api/analytics/track', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(analyticsEvent),
        }).catch(err => {
          console.warn('Failed to send analytics event:', err);
        });
      } else {
        console.log('Analytics Event:', analyticsEvent);
      }

      // Store locally for development
      const events = JSON.parse(localStorage.getItem('brandflow_analytics') || '[]');
      events.push(analyticsEvent);
      localStorage.setItem('brandflow_analytics', JSON.stringify(events.slice(-100))); // Keep last 100 events
    } catch (error) {
      console.warn('Failed to track analytics event:', error);
    }
  }, []);

  const getMetrics = useCallback((): PerformanceMetrics => {
    const events = JSON.parse(localStorage.getItem('brandflow_analytics') || '[]');
    
    const getAverageTime = (eventType: string) => {
      const relevantEvents = events.filter((e: AnalyticsEvent) => 
        e.type.includes(eventType) && e.properties.duration
      );
      
      if (relevantEvents.length === 0) return 0;
      
      const totalTime = relevantEvents.reduce((sum: number, e: AnalyticsEvent) => 
        sum + (e.properties.duration || 0), 0
      );
      
      return totalTime / relevantEvents.length;
    };

    return {
      brandKitUploadTime: getAverageTime('brand_kit_upload'),
      optimizationTime: getAverageTime('optimization'),
      exportTime: getAverageTime('export'),
      complianceCheckTime: getAverageTime('compliance_check'),
      aiAnalysisTime: getAverageTime('ai_analysis'),
    };
  }, []);

  const getUsageStats = useCallback((): UsageStatistics => {
    const events = JSON.parse(localStorage.getItem('brandflow_analytics') || '[]');
    
    const brandKitEvents = events.filter((e: AnalyticsEvent) => e.type === 'brand_kit_uploaded');
    const optimizationEvents = events.filter((e: AnalyticsEvent) => e.type === 'optimization_completed');
    const exportEvents = events.filter((e: AnalyticsEvent) => e.type === 'export_completed');
    
    // Count platform usage
    const platformCounts: Record<string, number> = {};
    optimizationEvents.forEach((e: AnalyticsEvent) => {
      if (e.properties.platforms) {
        e.properties.platforms.forEach((platform: string) => {
          platformCounts[platform] = (platformCounts[platform] || 0) + 1;
        });
      }
    });
    
    const totalPlatformUsage = Object.values(platformCounts).reduce((sum, count) => sum + count, 0);
    const popularPlatforms = Object.entries(platformCounts)
      .map(([platform, count]) => ({
        platform,
        count,
        percentage: totalPlatformUsage > 0 ? (count / totalPlatformUsage) * 100 : 0
      }))
      .sort((a, b) => b.count - a.count);

    // Calculate average compliance score
    const complianceEvents = events.filter((e: AnalyticsEvent) => 
      e.type === 'compliance_check' && e.properties.score
    );
    const averageComplianceScore = complianceEvents.length > 0
      ? complianceEvents.reduce((sum: number, e: AnalyticsEvent) => 
          sum + (e.properties.score || 0), 0) / complianceEvents.length
      : 0;

    return {
      totalBrandKits: brandKitEvents.length,
      totalOptimizations: optimizationEvents.length,
      totalExports: exportEvents.length,
      popularPlatforms,
      averageComplianceScore,
    };
  }, []);

  return {
    trackEvent,
    getMetrics,
    getUsageStats,
  };
};

// Utility function to get or create session ID
function getSessionId(): string {
  let sessionId = sessionStorage.getItem('brandflow_session_id');
  
  if (!sessionId) {
    sessionId = Date.now().toString(36) + Math.random().toString(36).substr(2);
    sessionStorage.setItem('brandflow_session_id', sessionId);
  }
  
  return sessionId;
}
