import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import {
  View,
  Flex,
  Heading,
  Text,
  Button,
  ProgressBarComponent as <PERSON><PERSON><PERSON>,
  ActionButton,
  <PERSON>,
  <PERSON><PERSON>,
  Item,
  Switch
} from './UI';
import { Download, Delete, Folder, CheckmarkCircle } from './Icons';

// Types
interface ExportItem {
  id: string;
  name: string;
  platform: string;
  format: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress: number;
  fileSize?: string;
  downloadUrl?: string;
  error?: string;
}

interface ExportQueueProps {
  variations: any[];
  onExportComplete: () => void;
  isExportReady: boolean;
}

// Styled Components
const QueueContainer = styled(View)`
  background: white;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.lg};
  overflow: hidden;
`;

const QueueHeader = styled(View)`
  padding: ${props => props.theme.spacing.lg};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
`;

const ExportItemRow = styled(View)<{ status: string }>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${props => props.theme.spacing.md};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  background: ${props => {
    switch (props.status) {
      case 'completed': return `${props.theme.colors.success}10`;
      case 'failed': return `${props.theme.colors.error}10`;
      case 'processing': return `${props.theme.colors.primary}10`;
      default: return 'transparent';
    }
  }};

  &:last-child {
    border-bottom: none;
  }
`;

const ItemInfo = styled(View)`
  flex: 1;
  margin-right: ${props => props.theme.spacing.md};
`;

const ItemActions = styled(View)`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const StatusIndicator = styled(View)<{ status: string }>`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${props => {
    switch (props.status) {
      case 'completed': return props.theme.colors.success;
      case 'failed': return props.theme.colors.error;
      case 'processing': return props.theme.colors.primary;
      default: return props.theme.colors.secondary;
    }
  }};
  margin-right: ${props => props.theme.spacing.sm};
`;

const ExportSettings = styled(View)`
  background: ${props => props.theme.colors.surface};
  padding: ${props => props.theme.spacing.lg};
  border-radius: ${props => props.theme.borderRadius.md};
  margin-bottom: ${props => props.theme.spacing.md};
`;

const ExportQueue: React.FC<ExportQueueProps> = ({
  variations,
  onExportComplete,
  isExportReady
}) => {
  const [exportItems, setExportItems] = useState<ExportItem[]>([]);
  const [exportFormat, setExportFormat] = useState('png');
  const [exportQuality, setExportQuality] = useState('high');
  const [batchExport, setBatchExport] = useState(true);
  const [isExporting, setIsExporting] = useState(false);

  // Initialize export items when variations are available
  useEffect(() => {
    if (isExportReady && variations.length > 0 && exportItems.length === 0) {
      const items: ExportItem[] = variations.map((variation, index) => ({
        id: `export-${Date.now()}-${index}`,
        name: variation.name || `Variation ${index + 1}`,
        platform: variation.platform || 'unknown',
        format: exportFormat,
        status: 'queued',
        progress: 0
      }));
      
      setExportItems(items);
    }
  }, [isExportReady, variations, exportFormat]);

  const handleStartExport = async () => {
    if (exportItems.length === 0) return;

    setIsExporting(true);

    try {
      if (batchExport) {
        // Export all items in batch
        await exportAllItems();
      } else {
        // Export items one by one
        for (const item of exportItems) {
          await exportSingleItem(item.id);
        }
      }

      onExportComplete();
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const exportAllItems = async () => {
    // Update all items to processing
    setExportItems(prev => prev.map(item => ({
      ...item,
      status: 'processing',
      progress: 0
    })));

    // Simulate batch export progress
    for (let progress = 0; progress <= 100; progress += 10) {
      await new Promise(resolve => setTimeout(resolve, 200));
      
      setExportItems(prev => prev.map(item => ({
        ...item,
        progress
      })));
    }

    // Complete all exports
    setExportItems(prev => prev.map((item, index) => ({
      ...item,
      status: 'completed',
      progress: 100,
      fileSize: `${Math.round(Math.random() * 500 + 100)}KB`,
      downloadUrl: `/api/exports/${item.id}.${exportFormat}`
    })));
  };

  const exportSingleItem = async (itemId: string) => {
    // Update item to processing
    setExportItems(prev => prev.map(item => 
      item.id === itemId 
        ? { ...item, status: 'processing', progress: 0 }
        : item
    ));

    // Simulate export progress
    for (let progress = 0; progress <= 100; progress += 20) {
      await new Promise(resolve => setTimeout(resolve, 300));
      
      setExportItems(prev => prev.map(item => 
        item.id === itemId 
          ? { ...item, progress }
          : item
      ));
    }

    // Complete export
    setExportItems(prev => prev.map(item => 
      item.id === itemId 
        ? {
            ...item,
            status: 'completed',
            progress: 100,
            fileSize: `${Math.round(Math.random() * 500 + 100)}KB`,
            downloadUrl: `/api/exports/${item.id}.${exportFormat}`
          }
        : item
    ));
  };

  const handleDownloadItem = async (item: ExportItem) => {
    if (!item.downloadUrl) return;

    try {
      // Create download link
      const link = document.createElement('a');
      link.href = item.downloadUrl;
      link.download = `${item.name.replace(/\s+/g, '-').toLowerCase()}.${item.format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  const handleRemoveItem = (itemId: string) => {
    setExportItems(prev => prev.filter(item => item.id !== itemId));
  };

  const handleDownloadAll = async () => {
    const completedItems = exportItems.filter(item => item.status === 'completed');
    
    for (const item of completedItems) {
      await handleDownloadItem(item);
      await new Promise(resolve => setTimeout(resolve, 100)); // Small delay between downloads
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'queued': return 'Queued';
      case 'processing': return 'Exporting...';
      case 'completed': return 'Ready';
      case 'failed': return 'Failed';
      default: return 'Unknown';
    }
  };

  const completedCount = exportItems.filter(item => item.status === 'completed').length;
  const totalCount = exportItems.length;

  if (!isExportReady) {
    return (
      <View padding="size-200">
        <Flex direction="column" alignItems="center" gap="size-200">
          <Heading level={3}>Export Queue</Heading>
          <Text>Generate variations to start exporting your optimized designs.</Text>
        </Flex>
      </View>
    );
  }

  return (
    <View padding="size-200">
      <Flex direction="column" gap="size-200">
        <Heading level={3}>Export Queue</Heading>
        <Text>
          Export your optimized designs in various formats for different platforms.
        </Text>

        {/* Export Settings */}
        <ExportSettings>
          <Flex direction="column" gap="size-200">
            <Heading level={4}>Export Settings</Heading>
            
            <Flex gap="size-200">
              <div>
                <label>Format</label>
                <Picker
                  value={exportFormat}
                  onChange={(e) => setExportFormat(e.target.value)}
                  style={{ width: '200px' }}
                >
                  <Item value="png">PNG</Item>
                  <Item value="jpg">JPG</Item>
                  <Item value="pdf">PDF</Item>
                  <Item value="svg">SVG</Item>
                </Picker>
              </div>

              <div>
                <label>Quality</label>
                <Picker
                  value={exportQuality}
                  onChange={(e) => setExportQuality(e.target.value)}
                  style={{ width: '200px' }}
                >
                  <Item value="high">High (100%)</Item>
                  <Item value="medium">Medium (80%)</Item>
                  <Item value="low">Low (60%)</Item>
                </Picker>
              </div>
            </Flex>

            <Switch isSelected={batchExport}>
              <input
                type="checkbox"
                checked={batchExport}
                onChange={(e) => setBatchExport(e.target.checked)}
              />
              Batch export (faster processing)
            </Switch>
          </Flex>
        </ExportSettings>

        {/* Export Queue */}
        <QueueContainer>
          <QueueHeader>
            <Flex justifyContent="space-between" alignItems="center">
              <Text>
                Export Queue ({completedCount}/{totalCount} completed)
              </Text>
              <Flex gap="size-100">
                {completedCount > 0 && (
                  <ActionButton onClick={handleDownloadAll}>
                    <Folder />
                    <Text>Download All</Text>
                  </ActionButton>
                )}
                <Button
                  variant="cta"
                  onClick={handleStartExport}
                  disabled={isExporting || exportItems.length === 0}
                >
                  {isExporting ? 'Exporting...' : 'Start Export'}
                </Button>
              </Flex>
            </Flex>
          </QueueHeader>

          {exportItems.length === 0 ? (
            <View padding="size-400">
              <Text>No items in export queue. Generate variations first.</Text>
            </View>
          ) : (
            exportItems.map((item) => (
              <ExportItemRow key={item.id} status={item.status}>
                <ItemInfo>
                  <Flex alignItems="center" gap="size-100">
                    <StatusIndicator status={item.status} />
                    <View>
                      <Text>{item.name}</Text>
                      <Text slot="description">
                        {item.platform} • {item.format.toUpperCase()}
                        {item.fileSize && ` • ${item.fileSize}`}
                      </Text>
                    </View>
                  </Flex>
                  
                  {item.status === 'processing' && (
                    <ProgressBar
                      value={item.progress}
                      maxValue={100}
                      showValueLabel
                      width="100%"
                      marginTop="size-100"
                    />
                  )}
                  
                  {item.error && (
                    <Text color="negative" marginTop="size-100">
                      {item.error}
                    </Text>
                  )}
                </ItemInfo>

                <ItemActions>
                  <Text>{getStatusText(item.status)}</Text>
                  
                  {item.status === 'completed' && (
                    <ActionButton onClick={() => handleDownloadItem(item)}>
                      <Download />
                    </ActionButton>
                  )}
                  
                  <ActionButton
                    onClick={() => handleRemoveItem(item.id)}
                    disabled={item.status === 'processing'}
                  >
                    <Delete />
                  </ActionButton>
                </ItemActions>
              </ExportItemRow>
            ))
          )}
        </QueueContainer>

        {completedCount === totalCount && totalCount > 0 && (
          <Well>
            <Flex alignItems="center" gap="size-100">
              <CheckmarkCircle color="positive" />
              <Text>All exports completed successfully!</Text>
            </Flex>
          </Well>
        )}
      </Flex>
    </View>
  );
};

export default ExportQueue;
