import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import {
  View,
  Flex,
  Heading,
  Text,
  Button,
  ProgressBarComponent as ProgressBar,
  ActionButton,
  Well
} from './UI';
import { Refresh, Download, Preview } from './Icons';

// Types
interface BrandKit {
  id: string;
  name: string;
  colors: any;
  fonts: any;
}

interface DocumentElement {
  id: string;
  type: string;
  bounds: any;
  properties: any;
}

interface DesignVariation {
  id: string;
  name: string;
  platform: string;
  previewUrl: string;
  status: 'generating' | 'ready' | 'error';
  dimensions: { width: number; height: number };
  optimizations: string[];
}

interface VariationGeneratorProps {
  brandKit: BrandKit | null;
  documentElements: DocumentElement[];
  onVariationsGenerated: () => void;
  isOptimizationComplete: boolean;
}

// Styled Components
const VariationGrid = styled(View)`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${props => props.theme.spacing.lg};
  margin-top: ${props => props.theme.spacing.md};
`;

const VariationCard = styled(View)<{ status: string }>`
  background: white;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.lg};
  overflow: hidden;
  transition: ${props => props.theme.transition};
  opacity: ${props => props.status === 'generating' ? 0.7 : 1};

  &:hover {
    box-shadow: ${props => props.theme.shadows.md};
  }
`;

const VariationPreview = styled(View)<{ status: string }>`
  aspect-ratio: 16/9;
  background: ${props => props.theme.colors.surface};
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const StatusOverlay = styled(View)<{ status: string }>`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  opacity: ${props => props.status === 'generating' ? 1 : 0};
  transition: opacity 0.3s ease;
`;

const VariationInfo = styled(View)`
  padding: ${props => props.theme.spacing.md};
`;

const VariationActions = styled(View)`
  padding: ${props => props.theme.spacing.md};
  border-top: 1px solid ${props => props.theme.colors.border};
  display: flex;
  gap: ${props => props.theme.spacing.sm};
  justify-content: space-between;
`;

const OptimizationTags = styled(View)`
  display: flex;
  flex-wrap: wrap;
  gap: ${props => props.theme.spacing.xs};
  margin-top: ${props => props.theme.spacing.sm};
`;

const OptimizationTag = styled(View)`
  background: ${props => props.theme.colors.primary}20;
  color: ${props => props.theme.colors.primary};
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: 12px;
`;

const VariationGenerator: React.FC<VariationGeneratorProps> = ({
  brandKit,
  documentElements,
  onVariationsGenerated,
  isOptimizationComplete
}) => {
  const [variations, setVariations] = useState<DesignVariation[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);

  // Generate variations when optimization is complete
  useEffect(() => {
    if (isOptimizationComplete && variations.length === 0) {
      generateVariations();
    }
  }, [isOptimizationComplete]);

  const generateVariations = async () => {
    setIsGenerating(true);
    setGenerationProgress(0);

    try {
      // Mock variation data for different platforms
      const platformVariations: Omit<DesignVariation, 'id'>[] = [
        {
          name: 'Instagram Post',
          platform: 'instagram-post',
          previewUrl: 'https://via.placeholder.com/400x400/0265DC/FFFFFF?text=Instagram+Post',
          status: 'generating',
          dimensions: { width: 1080, height: 1080 },
          optimizations: ['Square format', 'Mobile optimized', 'Brand colors applied']
        },
        {
          name: 'Instagram Story',
          platform: 'instagram-story',
          previewUrl: 'https://via.placeholder.com/400x711/0265DC/FFFFFF?text=Instagram+Story',
          status: 'generating',
          dimensions: { width: 1080, height: 1920 },
          optimizations: ['Vertical format', 'Story-safe area', 'Interactive elements']
        },
        {
          name: 'Facebook Post',
          platform: 'facebook-post',
          previewUrl: 'https://via.placeholder.com/400x225/0265DC/FFFFFF?text=Facebook+Post',
          status: 'generating',
          dimensions: { width: 1200, height: 675 },
          optimizations: ['Landscape format', 'News feed optimized', 'Link preview ready']
        },
        {
          name: 'Twitter Post',
          platform: 'twitter-post',
          previewUrl: 'https://via.placeholder.com/400x225/0265DC/FFFFFF?text=Twitter+Post',
          status: 'generating',
          dimensions: { width: 1200, height: 675 },
          optimizations: ['Timeline optimized', 'Compressed for 5MB limit', 'High contrast']
        },
        {
          name: 'LinkedIn Post',
          platform: 'linkedin-post',
          previewUrl: 'https://via.placeholder.com/400x209/0265DC/FFFFFF?text=LinkedIn+Post',
          status: 'generating',
          dimensions: { width: 1200, height: 627 },
          optimizations: ['Professional format', 'B2B optimized', 'Corporate branding']
        }
      ];

      // Create variations with unique IDs
      const newVariations: DesignVariation[] = platformVariations.map((variation, index) => ({
        ...variation,
        id: `variation-${Date.now()}-${index}`
      }));

      setVariations(newVariations);

      // Simulate generation progress
      for (let i = 0; i < newVariations.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setGenerationProgress(((i + 1) / newVariations.length) * 100);
        
        // Update variation status to ready
        setVariations(prev => prev.map(variation => 
          variation.id === newVariations[i].id 
            ? { ...variation, status: 'ready' }
            : variation
        ));
      }

      onVariationsGenerated();

    } catch (error) {
      console.error('Failed to generate variations:', error);
      
      // Mark all variations as error
      setVariations(prev => prev.map(variation => ({
        ...variation,
        status: 'error'
      })));
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
    }
  };

  const handleRegenerateVariation = async (variationId: string) => {
    setVariations(prev => prev.map(variation => 
      variation.id === variationId 
        ? { ...variation, status: 'generating' }
        : variation
    ));

    // Simulate regeneration
    await new Promise(resolve => setTimeout(resolve, 2000));

    setVariations(prev => prev.map(variation => 
      variation.id === variationId 
        ? { ...variation, status: 'ready' }
        : variation
    ));
  };

  const handlePreviewVariation = (variation: DesignVariation) => {
    // Open preview in a modal or new window
    window.open(variation.previewUrl, '_blank', 'width=800,height=600');
  };

  const handleDownloadVariation = async (variation: DesignVariation) => {
    try {
      // In a real implementation, this would trigger the export process
      const response = await fetch(`/api/export/variation/${variation.id}`);
      const blob = await response.blob();
      
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${variation.name.replace(/\s+/g, '-').toLowerCase()}.png`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  if (!isOptimizationComplete) {
    return (
      <View padding="size-200">
        <Flex direction="column" alignItems="center" gap="size-200">
          <Heading level={3}>Variation Generator</Heading>
          <Text>Complete the optimization step to generate platform-specific variations.</Text>
        </Flex>
      </View>
    );
  }

  return (
    <View padding="size-200">
      <Flex direction="column" gap="size-200">
        <Flex justifyContent="space-between" alignItems="center">
          <Heading level={3}>Design Variations</Heading>
          <ActionButton onClick={generateVariations} disabled={isGenerating}>
            <Refresh />
            <Text>Regenerate All</Text>
          </ActionButton>
        </Flex>

        <Text>
          AI-generated variations optimized for each platform while maintaining brand consistency.
        </Text>

        {isGenerating && (
          <Well>
            <Flex direction="column" gap="size-100">
              <Text>Generating platform-optimized variations...</Text>
              <ProgressBar
                value={generationProgress}
                maxValue={100}
                showValueLabel
              />
            </Flex>
          </Well>
        )}

        <VariationGrid>
          {variations.map((variation) => (
            <VariationCard key={variation.id} status={variation.status}>
              <VariationPreview status={variation.status}>
                <img 
                  src={variation.previewUrl} 
                  alt={variation.name}
                  style={{ 
                    filter: variation.status === 'generating' ? 'blur(2px)' : 'none' 
                  }}
                />
                <StatusOverlay status={variation.status}>
                  {variation.status === 'generating' && 'Generating...'}
                  {variation.status === 'error' && 'Generation Failed'}
                </StatusOverlay>
              </VariationPreview>

              <VariationInfo>
                <Flex direction="column" gap="size-100">
                  <Heading level={4}>{variation.name}</Heading>
                  <Text>{variation.dimensions.width} × {variation.dimensions.height}</Text>
                  
                  <OptimizationTags>
                    {variation.optimizations.map((optimization, index) => (
                      <OptimizationTag key={index}>
                        {optimization}
                      </OptimizationTag>
                    ))}
                  </OptimizationTags>
                </Flex>
              </VariationInfo>

              <VariationActions>
                <Flex gap="size-100">
                  <ActionButton
                    onClick={() => handlePreviewVariation(variation)}
                    disabled={variation.status !== 'ready'}
                  >
                    <Preview />
                  </ActionButton>
                  <ActionButton
                    onClick={() => handleRegenerateVariation(variation.id)}
                    disabled={variation.status === 'generating'}
                  >
                    <Refresh />
                  </ActionButton>
                </Flex>
                
                <Button
                  variant="cta"
                  onClick={() => handleDownloadVariation(variation)}
                  disabled={variation.status !== 'ready'}
                >
                  <Download />
                  <Text>Download</Text>
                </Button>
              </VariationActions>
            </VariationCard>
          ))}
        </VariationGrid>

        {variations.length > 0 && variations.every(v => v.status === 'ready') && (
          <Well>
            <Flex alignItems="center" gap="size-100">
              <Text>✅ All variations generated successfully!</Text>
              <Button variant="cta" onClick={() => console.log('Export all variations')}>
                Export All Variations
              </Button>
            </Flex>
          </Well>
        )}
      </Flex>
    </View>
  );
};

export default VariationGenerator;
