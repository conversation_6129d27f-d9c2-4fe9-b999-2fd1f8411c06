{"testId": "brandflow-pro-addon", "name": "BrandFlow Pro", "version": "1.0.0", "manifestVersion": 1, "requirements": {"apps": [{"name": "Express", "apiVersion": 1}]}, "entryPoints": [{"type": "panel", "id": "brandflow-main-panel"}], "main": "index.html", "uiEntry": {"type": "panel", "id": "brandflow-main-panel"}, "sandboxEntry": "sandbox/documentAPI.js", "permissions": {"webview": {"allow": "yes", "domains": ["https://api.openai.com", "https://graph.facebook.com", "https://api.twitter.com", "https://api.linkedin.com", "https://graph.instagram.com", "https://api.cloudinary.com", "https://analytics.google.com"]}, "oauth": {"scopes": ["https://www.googleapis.com/auth/analytics.readonly", "instagram_basic", "pages_show_list", "pages_read_engagement"]}, "localFileSystem": "no", "launchProcess": "no", "allowCodeGenerationFromStrings": "yes"}, "icons": [{"width": 24, "height": 24, "path": "icons/icon-24.png", "theme": ["lightest", "light"], "species": ["generic"]}, {"width": 24, "height": 24, "path": "icons/icon-24-dark.png", "theme": ["darkest", "dark"], "species": ["generic"]}, {"width": 48, "height": 48, "path": "icons/icon-48.png", "theme": ["lightest", "light"], "species": ["generic"]}, {"width": 48, "height": 48, "path": "icons/icon-48-dark.png", "theme": ["darkest", "dark"], "species": ["generic"]}], "host": {"app": "Express", "minVersion": "1.0.0"}, "description": "AI-powered brand consistency and multi-platform content optimization for Adobe Express. Transform one design into platform-perfect, brand-consistent content for all your marketing channels.", "keywords": ["brand", "consistency", "social media", "optimization", "AI", "automation", "workflow", "marketing", "content creation", "multi-platform"], "author": "HectorTa1989", "homepage": "https://github.com/HectorTa1989/brandflow-pro", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/brandflow-pro.git"}, "bugs": {"url": "https://github.com/HectorTa1989/brandflow-pro/issues"}, "license": "MIT"}