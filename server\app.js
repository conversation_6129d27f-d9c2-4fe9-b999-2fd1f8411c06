/**
 * BrandFlow Pro - Backend Server
 * Node.js Express server for handling API requests, file uploads,
 * and external service integrations
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
require('dotenv').config();

// Import routes
const brandRoutes = require('./routes/brand');
const optimizationRoutes = require('./routes/optimization');
const analyticsRoutes = require('./routes/analytics');

// Import models
const Brand = require('./models/Brand');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3001;

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://unpkg.com", "https://fonts.googleapis.com"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'", "https://unpkg.com"],
      imgSrc: ["'self'", "data:", "blob:", "https:"],
      connectSrc: ["'self'", "https://api.openai.com", "https://graph.facebook.com", "https://api.twitter.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'", "blob:"],
      frameSrc: ["'none'"],
    },
  },
}));

// CORS configuration
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://new.express.adobe.com', 'https://express.adobe.com']
    : ['http://localhost:5241', 'https://localhost:5241'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Body parsing middleware
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Logging middleware
app.use(morgan(process.env.NODE_ENV === 'production' ? 'combined' : 'dev'));

// File upload configuration
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, 'uploads');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
    files: 10 // Maximum 10 files
  },
  fileFilter: (req, file, cb) => {
    // Allow images, PDFs, and design files
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/svg+xml',
      'application/pdf',
      'application/postscript', // AI files
      'image/vnd.adobe.photoshop' // PSD files
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`File type ${file.mimetype} not allowed`), false);
    }
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  });
});

// API Routes
app.use('/api/brand', brandRoutes);
app.use('/api/optimization', optimizationRoutes);
app.use('/api/analytics', analyticsRoutes);

// Brand kit upload endpoint
app.post('/api/upload/brand-kit', upload.array('assets', 10), async (req, res) => {
  try {
    const { brandName, colors, fonts, guidelines } = req.body;
    const files = req.files;
    
    if (!files || files.length === 0) {
      return res.status(400).json({
        error: 'No files uploaded',
        message: 'Please upload at least one brand asset'
      });
    }
    
    // Process uploaded files
    const processedAssets = await Promise.all(
      files.map(async (file) => {
        const assetData = {
          filename: file.filename,
          originalName: file.originalname,
          mimetype: file.mimetype,
          size: file.size,
          path: file.path,
          uploadedAt: new Date()
        };
        
        // Extract metadata based on file type
        if (file.mimetype.startsWith('image/')) {
          // For images, we could extract dimensions, color palette, etc.
          assetData.type = 'image';
        } else if (file.mimetype === 'application/pdf') {
          assetData.type = 'document';
        }
        
        return assetData;
      })
    );
    
    // Create brand kit record
    const brandKit = {
      id: generateId(),
      name: brandName || 'Untitled Brand Kit',
      colors: colors ? JSON.parse(colors) : {},
      fonts: fonts ? JSON.parse(fonts) : {},
      guidelines: guidelines ? JSON.parse(guidelines) : {},
      assets: processedAssets,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    // Save to database (simplified - in production use proper DB)
    await saveBrandKit(brandKit);
    
    res.json({
      success: true,
      brandKit: {
        ...brandKit,
        assets: brandKit.assets.map(asset => ({
          ...asset,
          url: `/api/assets/${asset.filename}`
        }))
      },
      message: 'Brand kit uploaded successfully'
    });
    
  } catch (error) {
    console.error('Brand kit upload failed:', error);
    res.status(500).json({
      error: 'Upload failed',
      message: error.message
    });
  }
});

// Asset serving endpoint
app.get('/api/assets/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    const filePath = path.join(__dirname, 'uploads', filename);
    
    // Check if file exists
    try {
      await fs.access(filePath);
    } catch {
      return res.status(404).json({ error: 'Asset not found' });
    }
    
    // Set appropriate headers
    const ext = path.extname(filename).toLowerCase();
    const mimeTypes = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.svg': 'image/svg+xml',
      '.pdf': 'application/pdf'
    };
    
    const mimeType = mimeTypes[ext] || 'application/octet-stream';
    res.setHeader('Content-Type', mimeType);
    res.setHeader('Cache-Control', 'public, max-age=31536000'); // 1 year cache
    
    res.sendFile(filePath);
    
  } catch (error) {
    console.error('Asset serving failed:', error);
    res.status(500).json({ error: 'Failed to serve asset' });
  }
});

// AI analysis endpoint
app.post('/api/ai/analyze-brand', async (req, res) => {
  try {
    const { images, existingAnalysis } = req.body;
    
    // This would integrate with OpenAI API
    // For now, return mock analysis
    const analysis = {
      colors: {
        primary: '#0265DC',
        secondary: '#6B7280',
        accent: '#059669',
        neutral: ['#FFFFFF', '#F9FAFB', '#E5E7EB', '#111827']
      },
      fonts: {
        primary: 'Inter',
        secondary: 'Arial',
        weights: ['400', '500', '600', '700']
      },
      guidelines: {
        spacing: 16,
        borderRadius: 8,
        minLogoSize: 32,
        clearSpace: 24
      },
      confidence: 0.85,
      suggestions: [
        'Consider using more consistent spacing between elements',
        'Primary color has good contrast ratio for accessibility',
        'Font choices are modern and web-friendly'
      ]
    };
    
    res.json({
      success: true,
      analysis,
      processingTime: Math.random() * 2000 + 1000 // Mock processing time
    });
    
  } catch (error) {
    console.error('AI analysis failed:', error);
    res.status(500).json({
      error: 'Analysis failed',
      message: error.message
    });
  }
});

// Platform optimization endpoint
app.post('/api/optimize/platform', async (req, res) => {
  try {
    const { platformId, documentData, brandKit, settings } = req.body;
    
    // Mock optimization process
    const optimizationResult = {
      platformId,
      optimizedElements: [],
      changes: [
        'Resized canvas to platform specifications',
        'Adjusted text size for readability',
        'Applied brand color corrections',
        'Optimized image compression'
      ],
      compliance: {
        score: 95,
        issues: [],
        suggestions: []
      },
      processingTime: Math.random() * 3000 + 2000
    };
    
    res.json({
      success: true,
      result: optimizationResult
    });
    
  } catch (error) {
    console.error('Platform optimization failed:', error);
    res.status(500).json({
      error: 'Optimization failed',
      message: error.message
    });
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        error: 'File too large',
        message: 'File size must be less than 50MB'
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        error: 'Too many files',
        message: 'Maximum 10 files allowed'
      });
    }
  }
  
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Not found',
    message: `Route ${req.method} ${req.path} not found`
  });
});

// Utility functions
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

async function saveBrandKit(brandKit) {
  // In production, this would save to a proper database
  // For now, save to a JSON file
  const dataDir = path.join(__dirname, 'data');
  await fs.mkdir(dataDir, { recursive: true });
  
  const filePath = path.join(dataDir, `brand-kit-${brandKit.id}.json`);
  await fs.writeFile(filePath, JSON.stringify(brandKit, null, 2));
}

// Start server
app.listen(PORT, () => {
  console.log(`🚀 BrandFlow Pro server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
});

module.exports = app;
