/**
 * Platform Optimization Service
 * Handles optimization of designs for different social media platforms
 */

import { Platform, OptimizationSettings, OptimizationResult, BrandKit } from '../types/brand';

export async function optimizeForPlatform(
  document: any,
  platform: Platform,
  brandKit: BrandKit | null,
  settings: OptimizationSettings
): Promise<OptimizationResult> {
  try {
    console.log(`🎯 Starting optimization for ${platform.name}...`);
    
    const startTime = Date.now();
    
    // Send optimization request to document sandbox
    if (window.addOnUISdk) {
      window.addOnUISdk.app.document.postMessage({
        type: 'OPTIMIZE_FOR_PLATFORM',
        payload: {
          platform,
          settings,
          brandKit
        }
      });
    }
    
    // Simulate optimization process for demo
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const processingTime = Date.now() - startTime;
    
    const result: OptimizationResult = {
      platformId: platform.id,
      success: true,
      optimizedElements: [
        {
          id: 'canvas',
          type: 'canvas',
          originalBounds: { x: 0, y: 0, width: 1080, height: 1080 },
          optimizedBounds: { 
            x: 0, 
            y: 0, 
            width: platform.specs.dimensions.width, 
            height: platform.specs.dimensions.height 
          },
          changes: [
            {
              property: 'dimensions',
              oldValue: '1080x1080',
              newValue: `${platform.specs.dimensions.width}x${platform.specs.dimensions.height}`,
              reason: `Resized for ${platform.name} specifications`
            }
          ]
        }
      ],
      changes: [
        `Resized canvas to ${platform.specs.dimensions.width}x${platform.specs.dimensions.height}`,
        'Applied brand color corrections',
        'Optimized text size for readability',
        'Adjusted image compression for platform requirements'
      ],
      compliance: {
        overall: 'compliant',
        score: 95,
        issues: [],
        suggestions: []
      },
      processingTime,
      outputFiles: [
        {
          platform: platform.name,
          format: platform.specs.formats[0],
          url: `/api/exports/${platform.id}-optimized.${platform.specs.formats[0].toLowerCase()}`,
          size: calculateEstimatedFileSize(platform),
          dimensions: platform.specs.dimensions,
          optimizations: [
            'Smart cropping applied',
            'Color profile optimized',
            'Compression level adjusted'
          ]
        }
      ]
    };
    
    console.log(`✅ Optimization completed for ${platform.name} in ${processingTime}ms`);
    return result;
    
  } catch (error) {
    console.error(`❌ Optimization failed for ${platform.name}:`, error);
    
    return {
      platformId: platform.id,
      success: false,
      optimizedElements: [],
      changes: [],
      compliance: {
        overall: 'error',
        score: 0,
        issues: [
          {
            type: 'system',
            message: 'Optimization process failed',
            severity: 'high'
          }
        ],
        suggestions: ['Please try the optimization again']
      },
      processingTime: 0,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Calculate estimated file size based on platform specifications
 */
function calculateEstimatedFileSize(platform: Platform): number {
  const { width, height } = platform.specs.dimensions;
  const pixelCount = width * height;
  
  // Rough estimation based on format and dimensions
  const baseSize = pixelCount * 3; // 3 bytes per pixel for RGB
  
  // Apply compression estimates
  const compressionRatios: Record<string, number> = {
    'JPG': 0.1,
    'JPEG': 0.1,
    'PNG': 0.3,
    'WEBP': 0.08,
    'MP4': 0.05
  };
  
  const format = platform.specs.formats[0];
  const compressionRatio = compressionRatios[format] || 0.2;
  
  return Math.round(baseSize * compressionRatio);
}

/**
 * Get optimization recommendations for a platform
 */
export function getOptimizationRecommendations(platform: Platform): string[] {
  const recommendations: string[] = [];
  
  switch (platform.id) {
    case 'instagram-post':
      recommendations.push(
        'Use high-contrast colors for better mobile visibility',
        'Keep text large and readable on small screens',
        'Consider the square format when positioning elements'
      );
      break;
      
    case 'instagram-story':
      recommendations.push(
        'Place important content in the center to avoid UI overlap',
        'Use vertical-friendly layouts',
        'Consider interactive elements like polls or questions'
      );
      break;
      
    case 'facebook-post':
      recommendations.push(
        'Optimize for both desktop and mobile viewing',
        'Use landscape orientation effectively',
        'Include clear call-to-action text'
      );
      break;
      
    case 'twitter-post':
      recommendations.push(
        'Keep file size under 5MB for faster loading',
        'Use bold, eye-catching visuals',
        'Consider how the image will look in timeline preview'
      );
      break;
      
    case 'linkedin-post':
      recommendations.push(
        'Use professional color schemes',
        'Include company branding prominently',
        'Optimize for business context and audience'
      );
      break;
      
    case 'tiktok-video':
      recommendations.push(
        'Design for vertical mobile viewing',
        'Use bold, attention-grabbing elements',
        'Consider how static elements will work with video content'
      );
      break;
  }
  
  return recommendations;
}

/**
 * Validate platform compatibility
 */
export function validatePlatformCompatibility(
  document: any,
  platform: Platform
): { compatible: boolean; issues: string[] } {
  const issues: string[] = [];
  
  // Check dimensions
  if (document.width && document.height) {
    const aspectRatio = document.width / document.height;
    const platformAspectRatio = platform.specs.dimensions.width / platform.specs.dimensions.height;
    
    if (Math.abs(aspectRatio - platformAspectRatio) > 0.1) {
      issues.push(`Aspect ratio mismatch: document is ${aspectRatio.toFixed(2)}, platform expects ${platformAspectRatio.toFixed(2)}`);
    }
  }
  
  // Check file size limits
  const maxSizeBytes = parseFileSize(platform.specs.maxFileSize);
  if (document.estimatedSize && document.estimatedSize > maxSizeBytes) {
    issues.push(`File size may exceed platform limit of ${platform.specs.maxFileSize}`);
  }
  
  // Check text limits
  if (platform.specs.textLimits && document.textElements) {
    const totalTextLength = document.textElements.reduce(
      (sum: number, element: any) => sum + (element.text?.length || 0), 
      0
    );
    
    if (platform.specs.textLimits.title && totalTextLength > platform.specs.textLimits.title) {
      issues.push(`Text content exceeds platform limit of ${platform.specs.textLimits.title} characters`);
    }
  }
  
  return {
    compatible: issues.length === 0,
    issues
  };
}

/**
 * Parse file size string to bytes
 */
function parseFileSize(sizeString: string): number {
  const units: Record<string, number> = {
    'B': 1,
    'KB': 1024,
    'MB': 1024 * 1024,
    'GB': 1024 * 1024 * 1024
  };
  
  const match = sizeString.match(/^(\d+(?:\.\d+)?)\s*([A-Z]+)$/i);
  if (!match) return 0;
  
  const value = parseFloat(match[1]);
  const unit = match[2].toUpperCase();
  
  return value * (units[unit] || 1);
}

/**
 * Get platform-specific optimization settings
 */
export function getPlatformOptimizationSettings(platform: Platform): Partial<OptimizationSettings> {
  const settings: Partial<OptimizationSettings> = {};
  
  switch (platform.id) {
    case 'instagram-story':
    case 'tiktok-video':
      settings.optimizeForMobile = true;
      settings.smartCropping = true;
      break;
      
    case 'twitter-post':
      settings.compressionLevel = 85; // Higher compression for Twitter's 5MB limit
      break;
      
    case 'linkedin-post':
      settings.preserveTextReadability = true;
      settings.compressionLevel = 90; // Lower compression for professional quality
      break;
      
    default:
      settings.compressionLevel = 80;
      settings.optimizeForMobile = true;
  }
  
  return settings;
}
