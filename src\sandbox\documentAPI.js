/**
 * BrandFlow Pro - Document Sandbox API
 * This file contains all the Adobe Express Document API interactions
 * for brand compliance checking, content optimization, and export functionality.
 */

// Import Adobe Express Add-on SDK
import addOnUISdk from "https://new.express.adobe.com/static/add-on-sdk/sdk.js";

// Initialize the SDK
addOnUISdk.ready.then(async () => {
  console.log("✅ Adobe Express Add-on SDK initialized");
});

/**
 * Document API Functions
 * These functions interact with the Adobe Express document and provide
 * the core functionality for BrandFlow Pro
 */

// Global variables for document state
let currentDocument = null;
let documentElements = [];
let brandKit = null;

/**
 * Initialize Document API and set up communication with UI
 */
async function initializeDocumentAPI() {
  try {
    // Get current document
    currentDocument = addOnUISdk.app.document;
    
    // Set up event listeners for document changes
    currentDocument.addEventListener("documentChanged", handleDocumentChange);
    
    // Get initial document elements
    await refreshDocumentElements();
    
    console.log("📄 Document API initialized successfully");
    
    // Notify UI that document is ready
    addOnUISdk.app.ui.postMessage({
      type: "DOCUMENT_READY",
      payload: {
        documentId: currentDocument.id,
        elementCount: documentElements.length
      }
    });
    
  } catch (error) {
    console.error("❌ Failed to initialize Document API:", error);
    throw error;
  }
}

/**
 * Handle document changes
 */
function handleDocumentChange(event) {
  console.log("📝 Document changed:", event);
  
  // Refresh document elements
  refreshDocumentElements();
  
  // Notify UI of changes
  addOnUISdk.app.ui.postMessage({
    type: "DOCUMENT_CHANGED",
    payload: {
      changeType: event.type,
      elementCount: documentElements.length
    }
  });
}

/**
 * Refresh document elements array
 */
async function refreshDocumentElements() {
  try {
    const pages = currentDocument.pages;
    documentElements = [];
    
    for (const page of pages) {
      const pageElements = await getPageElements(page);
      documentElements.push(...pageElements);
    }
    
    console.log(`📊 Found ${documentElements.length} document elements`);
    return documentElements;
    
  } catch (error) {
    console.error("❌ Failed to refresh document elements:", error);
    return [];
  }
}

/**
 * Get all elements from a page
 */
async function getPageElements(page) {
  const elements = [];
  
  try {
    // Get all children of the page
    const children = page.children;
    
    for (const child of children) {
      const elementData = await extractElementData(child);
      if (elementData) {
        elements.push(elementData);
      }
    }
    
  } catch (error) {
    console.error("❌ Failed to get page elements:", error);
  }
  
  return elements;
}

/**
 * Extract data from a document element
 */
async function extractElementData(element) {
  try {
    const elementData = {
      id: element.id,
      type: element.type,
      bounds: element.boundsInParent,
      visible: element.visible,
      locked: element.locked
    };
    
    // Extract type-specific data
    switch (element.type) {
      case "text":
        elementData.text = element.text;
        elementData.fontSize = element.fontSize;
        elementData.fontFamily = element.fontFamily;
        elementData.fill = element.fill;
        break;
        
      case "rectangle":
      case "ellipse":
        elementData.fill = element.fill;
        elementData.stroke = element.stroke;
        elementData.cornerRadius = element.cornerRadius;
        break;
        
      case "image":
        elementData.src = element.src;
        elementData.naturalWidth = element.naturalWidth;
        elementData.naturalHeight = element.naturalHeight;
        break;
        
      case "group":
        elementData.children = [];
        for (const child of element.children) {
          const childData = await extractElementData(child);
          if (childData) {
            elementData.children.push(childData);
          }
        }
        break;
    }
    
    return elementData;
    
  } catch (error) {
    console.error("❌ Failed to extract element data:", error);
    return null;
  }
}

/**
 * Check brand compliance for current document
 */
async function checkBrandCompliance(brandKitData) {
  try {
    brandKit = brandKitData;
    const complianceResults = {
      overall: "compliant",
      issues: [],
      suggestions: []
    };
    
    // Check color compliance
    const colorIssues = await checkColorCompliance();
    if (colorIssues.length > 0) {
      complianceResults.issues.push(...colorIssues);
      complianceResults.overall = "warning";
    }
    
    // Check typography compliance
    const typographyIssues = await checkTypographyCompliance();
    if (typographyIssues.length > 0) {
      complianceResults.issues.push(...typographyIssues);
      complianceResults.overall = "warning";
    }
    
    // Check spacing compliance
    const spacingIssues = await checkSpacingCompliance();
    if (spacingIssues.length > 0) {
      complianceResults.issues.push(...spacingIssues);
    }
    
    console.log("🔍 Brand compliance check completed:", complianceResults);
    return complianceResults;
    
  } catch (error) {
    console.error("❌ Brand compliance check failed:", error);
    return {
      overall: "error",
      issues: ["Failed to check brand compliance"],
      suggestions: []
    };
  }
}

/**
 * Check color compliance against brand kit
 */
async function checkColorCompliance() {
  const issues = [];
  
  if (!brandKit || !brandKit.colors) {
    return issues;
  }
  
  const brandColors = [
    brandKit.colors.primary,
    brandKit.colors.secondary,
    brandKit.colors.accent,
    ...brandKit.colors.neutral
  ];
  
  for (const element of documentElements) {
    if (element.fill && element.fill.color) {
      const elementColor = element.fill.color;
      const isCompliant = brandColors.some(brandColor => 
        colorDistance(elementColor, brandColor) < 10
      );
      
      if (!isCompliant) {
        issues.push({
          type: "color",
          elementId: element.id,
          message: `Element uses non-brand color: ${elementColor}`,
          suggestion: `Consider using brand colors: ${brandColors.join(", ")}`
        });
      }
    }
  }
  
  return issues;
}

/**
 * Check typography compliance against brand kit
 */
async function checkTypographyCompliance() {
  const issues = [];
  
  if (!brandKit || !brandKit.fonts) {
    return issues;
  }
  
  const brandFonts = [brandKit.fonts.primary, brandKit.fonts.secondary];
  
  for (const element of documentElements) {
    if (element.type === "text" && element.fontFamily) {
      const isCompliant = brandFonts.includes(element.fontFamily);
      
      if (!isCompliant) {
        issues.push({
          type: "typography",
          elementId: element.id,
          message: `Text uses non-brand font: ${element.fontFamily}`,
          suggestion: `Use brand fonts: ${brandFonts.join(" or ")}`
        });
      }
    }
  }
  
  return issues;
}

/**
 * Check spacing compliance against brand guidelines
 */
async function checkSpacingCompliance() {
  const issues = [];
  
  if (!brandKit || !brandKit.guidelines) {
    return issues;
  }
  
  const minSpacing = brandKit.guidelines.spacing || 8;
  
  // Check spacing between elements
  for (let i = 0; i < documentElements.length - 1; i++) {
    for (let j = i + 1; j < documentElements.length; j++) {
      const element1 = documentElements[i];
      const element2 = documentElements[j];
      
      const distance = calculateElementDistance(element1, element2);
      
      if (distance > 0 && distance < minSpacing) {
        issues.push({
          type: "spacing",
          elementIds: [element1.id, element2.id],
          message: `Elements are too close (${distance}px)`,
          suggestion: `Maintain minimum spacing of ${minSpacing}px`
        });
      }
    }
  }
  
  return issues;
}

/**
 * Optimize document for specific platform
 */
async function optimizeForPlatform(platformConfig, optimizationSettings) {
  try {
    console.log(`🎯 Optimizing for ${platformConfig.name}...`);
    
    // Get current page
    const currentPage = currentDocument.pages[0];
    
    // Resize page to platform dimensions
    await resizePageForPlatform(currentPage, platformConfig);
    
    // Optimize text elements
    await optimizeTextElements(platformConfig, optimizationSettings);
    
    // Optimize image elements
    await optimizeImageElements(platformConfig, optimizationSettings);
    
    // Apply smart cropping if enabled
    if (optimizationSettings.smartCropping) {
      await applySmartCropping(platformConfig);
    }
    
    console.log(`✅ Optimization completed for ${platformConfig.name}`);
    
    // Notify UI of completion
    addOnUISdk.app.ui.postMessage({
      type: "OPTIMIZATION_COMPLETE",
      payload: {
        platform: platformConfig.name,
        success: true
      }
    });
    
  } catch (error) {
    console.error(`❌ Optimization failed for ${platformConfig.name}:`, error);
    
    addOnUISdk.app.ui.postMessage({
      type: "OPTIMIZATION_COMPLETE",
      payload: {
        platform: platformConfig.name,
        success: false,
        error: error.message
      }
    });
  }
}

/**
 * Resize page for platform specifications
 */
async function resizePageForPlatform(page, platformConfig) {
  try {
    const { width, height } = platformConfig.specs.dimensions;
    
    // Resize the page
    page.width = width;
    page.height = height;
    
    console.log(`📏 Page resized to ${width}x${height} for ${platformConfig.name}`);
    
  } catch (error) {
    console.error("❌ Failed to resize page:", error);
  }
}

/**
 * Optimize text elements for platform
 */
async function optimizeTextElements(platformConfig, settings) {
  const textElements = documentElements.filter(el => el.type === "text");
  
  for (const element of textElements) {
    try {
      // Get the actual element from document
      const docElement = currentDocument.getElementById(element.id);
      
      if (docElement) {
        // Adjust font size for platform
        const scaleFactor = calculateScaleFactor(platformConfig);
        docElement.fontSize = Math.max(12, element.fontSize * scaleFactor);
        
        // Ensure text readability
        if (settings.preserveTextReadability) {
          await ensureTextReadability(docElement, platformConfig);
        }
      }
      
    } catch (error) {
      console.error(`❌ Failed to optimize text element ${element.id}:`, error);
    }
  }
}

/**
 * Optimize image elements for platform
 */
async function optimizeImageElements(platformConfig, settings) {
  const imageElements = documentElements.filter(el => el.type === "image");
  
  for (const element of imageElements) {
    try {
      // Get the actual element from document
      const docElement = currentDocument.getElementById(element.id);
      
      if (docElement) {
        // Optimize for mobile if enabled
        if (settings.optimizeForMobile) {
          await optimizeImageForMobile(docElement, platformConfig);
        }
        
        // Apply compression
        await applyImageCompression(docElement, settings.compressionLevel);
      }
      
    } catch (error) {
      console.error(`❌ Failed to optimize image element ${element.id}:`, error);
    }
  }
}

/**
 * Create rendition for export
 */
async function createRendition(format, quality) {
  try {
    const renditionOptions = {
      format: format,
      quality: quality,
      range: "currentPage"
    };
    
    const rendition = await currentDocument.createRenditions([renditionOptions]);
    
    console.log(`📤 Rendition created in ${format} format`);
    return rendition[0];
    
  } catch (error) {
    console.error("❌ Failed to create rendition:", error);
    throw error;
  }
}

/**
 * Utility Functions
 */

// Calculate color distance
function colorDistance(color1, color2) {
  // Simple RGB distance calculation
  const r1 = parseInt(color1.slice(1, 3), 16);
  const g1 = parseInt(color1.slice(3, 5), 16);
  const b1 = parseInt(color1.slice(5, 7), 16);
  
  const r2 = parseInt(color2.slice(1, 3), 16);
  const g2 = parseInt(color2.slice(3, 5), 16);
  const b2 = parseInt(color2.slice(5, 7), 16);
  
  return Math.sqrt(
    Math.pow(r2 - r1, 2) + 
    Math.pow(g2 - g1, 2) + 
    Math.pow(b2 - b1, 2)
  );
}

// Calculate distance between elements
function calculateElementDistance(element1, element2) {
  const bounds1 = element1.bounds;
  const bounds2 = element2.bounds;
  
  const centerX1 = bounds1.x + bounds1.width / 2;
  const centerY1 = bounds1.y + bounds1.height / 2;
  const centerX2 = bounds2.x + bounds2.width / 2;
  const centerY2 = bounds2.y + bounds2.height / 2;
  
  return Math.sqrt(
    Math.pow(centerX2 - centerX1, 2) + 
    Math.pow(centerY2 - centerY1, 2)
  );
}

// Calculate scale factor for platform
function calculateScaleFactor(platformConfig) {
  const baseWidth = 1080; // Standard base width
  const platformWidth = platformConfig.specs.dimensions.width;
  return platformWidth / baseWidth;
}

/**
 * Message Handler for UI Communication
 */
addOnUISdk.app.on("message", async (message) => {
  const { type, payload } = message;
  
  try {
    switch (type) {
      case "INIT_DOCUMENT_API":
        await initializeDocumentAPI();
        break;
        
      case "CHECK_BRAND_COMPLIANCE":
        const complianceResult = await checkBrandCompliance(payload.brandKit);
        addOnUISdk.app.ui.postMessage({
          type: "BRAND_COMPLIANCE_RESULT",
          payload: complianceResult
        });
        break;
        
      case "OPTIMIZE_FOR_PLATFORM":
        await optimizeForPlatform(payload.platform, payload.settings);
        break;
        
      case "CREATE_RENDITION":
        const rendition = await createRendition(payload.format, payload.quality);
        addOnUISdk.app.ui.postMessage({
          type: "RENDITION_CREATED",
          payload: { rendition }
        });
        break;
        
      case "GET_DOCUMENT_ELEMENTS":
        const elements = await refreshDocumentElements();
        addOnUISdk.app.ui.postMessage({
          type: "DOCUMENT_ELEMENTS",
          payload: { elements }
        });
        break;
        
      default:
        console.warn("Unknown message type:", type);
    }
    
  } catch (error) {
    console.error(`❌ Error handling message ${type}:`, error);
    
    addOnUISdk.app.ui.postMessage({
      type: "ERROR",
      payload: {
        message: error.message,
        originalType: type
      }
    });
  }
});

// Initialize when script loads
initializeDocumentAPI();
