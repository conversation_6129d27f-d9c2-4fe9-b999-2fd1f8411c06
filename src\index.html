<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BrandFlow Pro - Adobe Express Add-on</title>
    <meta name="description" content="AI-powered brand consistency and multi-platform content optimization">
    
    <!-- Adobe Spectrum CSS -->
    <link rel="stylesheet" href="https://unpkg.com/@adobe/spectrum-css@latest/dist/spectrum-core.css">
    <link rel="stylesheet" href="https://unpkg.com/@adobe/spectrum-css@latest/dist/spectrum-light.css">
    <link rel="stylesheet" href="https://unpkg.com/@adobe/spectrum-css@latest/dist/spectrum-medium.css">
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="styles.css">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" as="style">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    
    <!-- Security headers -->
    <meta http-equiv="Content-Security-Policy" content="
        default-src 'self';
        script-src 'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com;
        style-src 'self' 'unsafe-inline' https://unpkg.com https://fonts.googleapis.com;
        font-src 'self' https://fonts.gstatic.com;
        img-src 'self' data: blob: https:;
        connect-src 'self' https://api.openai.com https://graph.facebook.com https://api.twitter.com https://api.linkedin.com https://graph.instagram.com https://api.cloudinary.com https://analytics.google.com;
        frame-src 'none';
        object-src 'none';
        base-uri 'self';
    ">
</head>
<body>
    <!-- Main Application Container -->
    <div id="root" class="brandflow-app">
        <!-- Loading State -->
        <div id="loading-screen" class="loading-container">
            <div class="loading-spinner">
                <div class="spinner-ring"></div>
            </div>
            <h3 class="loading-title">BrandFlow Pro</h3>
            <p class="loading-subtitle">Initializing AI-powered brand optimization...</p>
        </div>
        
        <!-- Error Boundary Fallback -->
        <div id="error-fallback" class="error-container" style="display: none;">
            <div class="error-icon">⚠️</div>
            <h3 class="error-title">Something went wrong</h3>
            <p class="error-message">We're having trouble loading BrandFlow Pro. Please try refreshing the add-on.</p>
            <button id="retry-button" class="spectrum-Button spectrum-Button--cta">
                <span class="spectrum-Button-label">Retry</span>
            </button>
        </div>
    </div>

    <!-- Hidden Templates for Dynamic Content -->
    <template id="brand-kit-template">
        <div class="brand-kit-item">
            <div class="brand-kit-preview"></div>
            <div class="brand-kit-info">
                <h4 class="brand-kit-name"></h4>
                <p class="brand-kit-description"></p>
            </div>
            <div class="brand-kit-actions">
                <button class="spectrum-Button spectrum-Button--secondary spectrum-Button--sizeS">
                    <span class="spectrum-Button-label">Edit</span>
                </button>
                <button class="spectrum-Button spectrum-Button--negative spectrum-Button--sizeS">
                    <span class="spectrum-Button-label">Delete</span>
                </button>
            </div>
        </div>
    </template>

    <template id="platform-option-template">
        <div class="platform-option">
            <div class="platform-icon"></div>
            <div class="platform-info">
                <h4 class="platform-name"></h4>
                <p class="platform-specs"></p>
            </div>
            <div class="platform-toggle">
                <input type="checkbox" class="spectrum-Checkbox-input">
                <span class="spectrum-Checkbox-box">
                    <svg class="spectrum-Icon spectrum-UIIcon-Checkmark100" focusable="false" aria-hidden="true">
                        <use xlink:href="#spectrum-css-icon-Checkmark100"></use>
                    </svg>
                </span>
            </div>
        </div>
    </template>

    <template id="variation-preview-template">
        <div class="variation-preview">
            <div class="variation-image"></div>
            <div class="variation-info">
                <h4 class="variation-title"></h4>
                <p class="variation-platform"></p>
                <div class="variation-specs"></div>
            </div>
            <div class="variation-actions">
                <button class="spectrum-Button spectrum-Button--secondary spectrum-Button--sizeS">
                    <span class="spectrum-Button-label">Preview</span>
                </button>
                <button class="spectrum-Button spectrum-Button--cta spectrum-Button--sizeS">
                    <span class="spectrum-Button-label">Export</span>
                </button>
            </div>
        </div>
    </template>

    <!-- SVG Icons -->
    <svg style="display: none;">
        <defs>
            <symbol id="spectrum-css-icon-Checkmark100" viewBox="0 0 10 10">
                <path d="M3.5 9.5a.999.999 0 01-.774-.368l-2.45-3a1 1 0 111.548-1.264L3.5 7.159l5.676-5.925a1 1 0 011.448 1.382l-6.45 6.75A.999.999 0 013.5 9.5z" fill="currentColor"/>
            </symbol>
            <symbol id="brand-icon" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
            </symbol>
            <symbol id="ai-icon" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" fill="currentColor"/>
            </symbol>
        </defs>
    </svg>

    <!-- Application Scripts -->
    <script type="module" src="index.tsx"></script>
    
    <!-- Error Handling Script -->
    <script>
        // Global error handler
        window.addEventListener('error', function(event) {
            console.error('Global error:', event.error);
            showErrorFallback('An unexpected error occurred. Please try again.');
        });

        window.addEventListener('unhandledrejection', function(event) {
            console.error('Unhandled promise rejection:', event.reason);
            showErrorFallback('Failed to load required resources. Please check your connection.');
        });

        function showErrorFallback(message) {
            const loadingScreen = document.getElementById('loading-screen');
            const errorFallback = document.getElementById('error-fallback');
            const errorMessage = document.querySelector('.error-message');
            
            if (loadingScreen) loadingScreen.style.display = 'none';
            if (errorFallback) {
                errorFallback.style.display = 'flex';
                if (errorMessage && message) {
                    errorMessage.textContent = message;
                }
            }
        }

        // Retry functionality
        document.addEventListener('DOMContentLoaded', function() {
            const retryButton = document.getElementById('retry-button');
            if (retryButton) {
                retryButton.addEventListener('click', function() {
                    window.location.reload();
                });
            }
        });

        // Theme detection and application
        function applyTheme() {
            const isDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
            document.documentElement.classList.toggle('spectrum--dark', isDark);
            document.documentElement.classList.toggle('spectrum--light', !isDark);
        }

        // Apply theme on load and when system preference changes
        applyTheme();
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', applyTheme);
        }
    </script>
</body>
</html>
