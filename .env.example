# BrandFlow Pro - Environment Configuration
# Copy this file to .env and fill in your actual values

# Application Environment
NODE_ENV=development
PORT=3001

# Adobe Express Add-on Configuration
ADDON_ID=brandflow-pro-addon
ADDON_VERSION=1.0.0

# OpenAI API Configuration
REACT_APP_OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4-vision-preview
OPENAI_MAX_TOKENS=1000

# Social Media API Keys
# Instagram Basic Display API
INSTAGRAM_APP_ID=your_instagram_app_id
INSTAGRAM_APP_SECRET=your_instagram_app_secret
INSTAGRAM_REDIRECT_URI=https://localhost:5241/auth/instagram/callback

# Facebook Graph API
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
FACEBOOK_REDIRECT_URI=https://localhost:5241/auth/facebook/callback

# Twitter API v2
TWITTER_CLIENT_ID=your_twitter_client_id
TWITTER_CLIENT_SECRET=your_twitter_client_secret
TWITTER_REDIRECT_URI=https://localhost:5241/auth/twitter/callback

# LinkedIn API
LINKEDIN_CLIENT_ID=your_linkedin_client_id
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret
LINKEDIN_REDIRECT_URI=https://localhost:5241/auth/linkedin/callback

# TikTok API (if available)
TIKTOK_CLIENT_KEY=your_tiktok_client_key
TIKTOK_CLIENT_SECRET=your_tiktok_client_secret

# Google Analytics API
GOOGLE_ANALYTICS_CLIENT_ID=your_google_analytics_client_id
GOOGLE_ANALYTICS_CLIENT_SECRET=your_google_analytics_client_secret
GOOGLE_ANALYTICS_REDIRECT_URI=https://localhost:5241/auth/google/callback

# Database Configuration (for production)
DATABASE_URL=mongodb://localhost:27017/brandflow-pro
REDIS_URL=redis://localhost:6379

# File Storage Configuration
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=52428800
MAX_FILES=10
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/svg+xml,application/pdf

# Image Processing
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# Security Configuration
JWT_SECRET=your_jwt_secret_key_here
SESSION_SECRET=your_session_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/brandflow-pro.log

# Analytics and Monitoring
ANALYTICS_ENABLED=true
SENTRY_DSN=your_sentry_dsn_here
GOOGLE_ANALYTICS_ID=your_google_analytics_id

# Feature Flags
FEATURE_AI_OPTIMIZATION=true
FEATURE_SOCIAL_MEDIA_INTEGRATION=true
FEATURE_ANALYTICS=true
FEATURE_COLLABORATION=false
FEATURE_PREMIUM_CONTENT=false

# API Endpoints
API_BASE_URL=http://localhost:3001
FRONTEND_URL=https://localhost:5241

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=100

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
FROM_EMAIL=<EMAIL>

# Webhook Configuration
WEBHOOK_SECRET=your_webhook_secret
WEBHOOK_URL=https://your-domain.com/webhooks

# Development Configuration
HOT_RELOAD=true
DEBUG_MODE=true
MOCK_EXTERNAL_APIS=false

# Production Configuration
# Uncomment and configure for production deployment

# SSL Configuration
# SSL_CERT_PATH=/path/to/ssl/cert.pem
# SSL_KEY_PATH=/path/to/ssl/key.pem

# CDN Configuration
# CDN_URL=https://cdn.brandflowpro.com
# STATIC_ASSETS_URL=https://assets.brandflowpro.com

# Load Balancer Configuration
# TRUST_PROXY=true
# PROXY_COUNT=1

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30000

# Backup Configuration
# BACKUP_ENABLED=true
# BACKUP_INTERVAL=86400000
# BACKUP_RETENTION_DAYS=30
# BACKUP_STORAGE_PATH=/backups

# Monitoring Configuration
# METRICS_ENABLED=true
# METRICS_PORT=9090
# PROMETHEUS_ENDPOINT=/metrics

# Content Security Policy
CSP_ENABLED=true
CSP_REPORT_URI=/csp-report

# CORS Configuration
CORS_ORIGIN=https://localhost:5241,https://new.express.adobe.com
CORS_CREDENTIALS=true

# API Versioning
API_VERSION=v1
API_PREFIX=/api

# Internationalization
DEFAULT_LOCALE=en
SUPPORTED_LOCALES=en,es,fr,de,it,pt,ja,ko,zh

# Performance Configuration
COMPRESSION_ENABLED=true
GZIP_LEVEL=6
RESPONSE_TIMEOUT=30000

# Security Headers
HSTS_MAX_AGE=31536000
HSTS_INCLUDE_SUBDOMAINS=true
HSTS_PRELOAD=true

# Content Type Validation
VALIDATE_CONTENT_TYPE=true
MAX_REQUEST_SIZE=52428800

# Session Configuration
SESSION_TIMEOUT=3600000
SESSION_SECURE=true
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=strict

# OAuth Configuration
OAUTH_STATE_SECRET=your_oauth_state_secret
OAUTH_TOKEN_EXPIRY=3600

# Webhook Retry Configuration
WEBHOOK_RETRY_ATTEMPTS=3
WEBHOOK_RETRY_DELAY=1000

# Queue Configuration
QUEUE_ENABLED=false
QUEUE_REDIS_URL=redis://localhost:6379
QUEUE_CONCURRENCY=5

# Notification Configuration
NOTIFICATIONS_ENABLED=true
PUSH_NOTIFICATION_KEY=your_push_notification_key

# A/B Testing Configuration
AB_TESTING_ENABLED=false
AB_TESTING_PERCENTAGE=10

# Feature Rollout Configuration
FEATURE_ROLLOUT_PERCENTAGE=100
BETA_FEATURES_ENABLED=false

# Documentation
API_DOCS_ENABLED=true
API_DOCS_PATH=/docs
SWAGGER_UI_ENABLED=true
