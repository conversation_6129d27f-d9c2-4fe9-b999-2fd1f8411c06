/**
 * BrandFlow Pro - TypeScript Type Definitions
 * Comprehensive type definitions for brand management and optimization
 */

// Core Brand Types
export interface BrandKit {
  id: string;
  name: string;
  colors: BrandColors;
  fonts: BrandFonts;
  logos: BrandLogos;
  guidelines: BrandGuidelines;
  assets: BrandAsset[];
  createdAt: Date;
  updatedAt: Date;
  version: string;
  isActive: boolean;
}

export interface BrandColors {
  primary: string;
  secondary: string;
  accent: string;
  neutral: string[];
  gradients?: BrandGradient[];
}

export interface BrandGradient {
  id: string;
  name: string;
  colors: string[];
  direction: 'linear' | 'radial';
  angle?: number;
}

export interface BrandFonts {
  primary: string;
  secondary: string;
  weights: string[];
  fallbacks?: string[];
  webFontUrls?: string[];
}

export interface BrandLogos {
  primary: File | string | null;
  secondary: File | string | null;
  icon: File | string | null;
  variations?: LogoVariation[];
}

export interface LogoVariation {
  id: string;
  name: string;
  file: File | string;
  usage: 'light' | 'dark' | 'color' | 'monochrome';
  minSize: number;
}

export interface BrandGuidelines {
  spacing: number;
  borderRadius: number;
  minLogoSize: number;
  clearSpace: number;
  colorUsage?: ColorUsageRule[];
  typographyRules?: TypographyRule[];
}

export interface ColorUsageRule {
  color: string;
  usage: 'primary' | 'secondary' | 'accent' | 'background' | 'text';
  restrictions?: string[];
  accessibility: {
    contrastRatio: number;
    wcagLevel: 'AA' | 'AAA';
  };
}

export interface TypographyRule {
  element: 'heading' | 'body' | 'caption' | 'button';
  font: string;
  size: number;
  weight: string;
  lineHeight: number;
  letterSpacing?: number;
}

export interface BrandAsset {
  id: string;
  filename: string;
  originalName: string;
  mimetype: string;
  size: number;
  path: string;
  url?: string;
  type: 'image' | 'document' | 'video' | 'audio';
  metadata?: AssetMetadata;
  uploadedAt: Date;
}

export interface AssetMetadata {
  width?: number;
  height?: number;
  duration?: number;
  colorPalette?: string[];
  dominantColor?: string;
  hasTransparency?: boolean;
  format?: string;
}

// Analysis Types
export interface BrandAnalysisResult {
  colors: BrandColors;
  fonts: BrandFonts;
  guidelines: BrandGuidelines;
  confidence: number;
  suggestions: string[];
  extractedElements?: ExtractedElement[];
}

export interface ExtractedElement {
  type: 'color' | 'font' | 'logo' | 'pattern';
  value: string;
  confidence: number;
  source: string;
  usage: string[];
}

// Compliance Types
export interface ComplianceResult {
  overall: 'compliant' | 'warning' | 'error';
  score: number;
  issues: ComplianceIssue[];
  suggestions: string[];
  platformSpecific?: PlatformComplianceIssue[];
}

export interface ComplianceIssue {
  type: 'color' | 'typography' | 'spacing' | 'logo' | 'accessibility';
  message: string;
  severity: 'low' | 'medium' | 'high';
  elementId?: string;
  suggestion?: string;
  autoFixAvailable?: boolean;
}

export interface PlatformComplianceIssue extends ComplianceIssue {
  platform: string;
  requirement: string;
  currentValue: string;
  expectedValue: string;
}

// Platform Types
export interface Platform {
  id: string;
  name: string;
  icon: string;
  category: 'social' | 'print' | 'web' | 'mobile';
  specs: PlatformSpecs;
  features: string[];
  selected: boolean;
  priority?: number;
}

export interface PlatformSpecs {
  aspectRatio: string;
  dimensions: {
    width: number;
    height: number;
  };
  maxFileSize: string;
  formats: string[];
  textLimits?: {
    title: number;
    description: number;
    hashtags?: number;
  };
  colorProfile?: 'sRGB' | 'P3' | 'CMYK';
  dpi?: number;
}

// Optimization Types
export interface OptimizationSettings {
  maintainAspectRatio: boolean;
  preserveTextReadability: boolean;
  optimizeForMobile: boolean;
  compressionLevel: number;
  colorProfileConversion: boolean;
  smartCropping: boolean;
  aiEnhancement?: boolean;
  batchProcessing?: boolean;
}

export interface OptimizationResult {
  platformId: string;
  success: boolean;
  optimizedElements: OptimizedElement[];
  changes: string[];
  compliance: ComplianceResult;
  processingTime: number;
  outputFiles?: OutputFile[];
  error?: string;
}

export interface OptimizedElement {
  id: string;
  type: string;
  originalBounds: ElementBounds;
  optimizedBounds: ElementBounds;
  changes: ElementChange[];
}

export interface ElementBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface ElementChange {
  property: string;
  oldValue: any;
  newValue: any;
  reason: string;
}

export interface OutputFile {
  platform: string;
  format: string;
  url: string;
  size: number;
  dimensions: {
    width: number;
    height: number;
  };
  optimizations: string[];
}

// Document Types
export interface DocumentElement {
  id: string;
  type: 'text' | 'image' | 'shape' | 'group';
  bounds: ElementBounds;
  visible: boolean;
  locked: boolean;
  properties: ElementProperties;
  children?: DocumentElement[];
}

export interface ElementProperties {
  // Text properties
  text?: string;
  fontSize?: number;
  fontFamily?: string;
  fontWeight?: string;
  color?: string;
  textAlign?: 'left' | 'center' | 'right' | 'justify';
  
  // Shape properties
  fill?: string;
  stroke?: string;
  strokeWidth?: number;
  cornerRadius?: number;
  
  // Image properties
  src?: string;
  naturalWidth?: number;
  naturalHeight?: number;
  opacity?: number;
  
  // Transform properties
  rotation?: number;
  scaleX?: number;
  scaleY?: number;
}

// Variation Types
export interface DesignVariation {
  id: string;
  name: string;
  platform: Platform;
  previewUrl: string;
  status: 'generating' | 'ready' | 'error';
  elements: DocumentElement[];
  metadata: VariationMetadata;
  createdAt: Date;
}

export interface VariationMetadata {
  originalDesignId: string;
  optimizations: string[];
  complianceScore: number;
  fileSize: number;
  processingTime: number;
  aiGenerated: boolean;
}

// Export Types
export interface ExportJob {
  id: string;
  variations: string[];
  format: 'png' | 'jpg' | 'pdf' | 'svg' | 'mp4';
  quality: number;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress: number;
  results?: ExportResult[];
  error?: string;
  createdAt: Date;
  completedAt?: Date;
}

export interface ExportResult {
  variationId: string;
  url: string;
  size: number;
  format: string;
  metadata: {
    width: number;
    height: number;
    colorProfile: string;
    compression: number;
  };
}

// Analytics Types
export interface AnalyticsEvent {
  type: string;
  properties: Record<string, any>;
  timestamp: Date;
  userId?: string;
  sessionId: string;
}

export interface PerformanceMetrics {
  brandKitUploadTime: number;
  optimizationTime: number;
  exportTime: number;
  complianceCheckTime: number;
  aiAnalysisTime: number;
}

export interface UsageStatistics {
  totalBrandKits: number;
  totalOptimizations: number;
  totalExports: number;
  popularPlatforms: PlatformUsage[];
  averageComplianceScore: number;
}

export interface PlatformUsage {
  platform: string;
  count: number;
  percentage: number;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// Error Types
export interface BrandFlowError {
  code: string;
  message: string;
  details?: Record<string, any>;
  stack?: string;
}

// Configuration Types
export interface AppConfig {
  apiEndpoint: string;
  openaiApiKey: string;
  environment: 'development' | 'production';
  features: FeatureFlags;
  limits: AppLimits;
}

export interface FeatureFlags {
  aiOptimization: boolean;
  socialMediaIntegration: boolean;
  analytics: boolean;
  collaboration: boolean;
  premiumContent: boolean;
  batchProcessing: boolean;
}

export interface AppLimits {
  maxFileSize: number;
  maxFiles: number;
  maxBrandKits: number;
  maxVariations: number;
  apiRateLimit: number;
}

// Utility Types
export type BrandKitStatus = 'draft' | 'active' | 'archived';
export type OptimizationStatus = 'pending' | 'processing' | 'completed' | 'failed';
export type ComplianceLevel = 'strict' | 'moderate' | 'lenient';
export type ExportFormat = 'png' | 'jpg' | 'pdf' | 'svg' | 'mp4' | 'gif';
export type ColorFormat = 'hex' | 'rgb' | 'hsl' | 'cmyk';

// Event Types for React Components
export interface BrandKitUploadEvent {
  files: FileList;
  brandKit?: Partial<BrandKit>;
}

export interface PlatformSelectionEvent {
  platforms: Platform[];
  settings: OptimizationSettings;
}

export interface ExportRequestEvent {
  variations: string[];
  format: ExportFormat;
  quality: number;
}

// Hook Return Types
export interface UseBrandKitReturn {
  brandKit: BrandKit | null;
  loading: boolean;
  error: string | null;
  uploadBrandKit: (files: FileList) => Promise<void>;
  updateBrandKit: (updates: Partial<BrandKit>) => Promise<void>;
  deleteBrandKit: (id: string) => Promise<void>;
  validateBrandKit: (brandKit: BrandKit) => ComplianceResult;
}

export interface UseOptimizationReturn {
  optimizations: OptimizationResult[];
  loading: boolean;
  error: string | null;
  optimizeForPlatforms: (platforms: Platform[], settings: OptimizationSettings) => Promise<void>;
  getOptimizationStatus: (id: string) => OptimizationStatus;
}

export interface UseAnalyticsReturn {
  trackEvent: (event: string, properties?: Record<string, any>) => void;
  getMetrics: () => PerformanceMetrics;
  getUsageStats: () => UsageStatistics;
}
