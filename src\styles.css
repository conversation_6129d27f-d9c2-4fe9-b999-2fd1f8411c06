/**
 * BrandFlow Pro - Global Styles
 * Custom CSS for Adobe Express Add-on with Adobe Spectrum integration
 */

/* CSS Custom Properties for Theme Support */
:root {
  /* Brand Colors */
  --brandflow-primary: #0265DC;
  --brandflow-secondary: #6B7280;
  --brandflow-success: #059669;
  --brandflow-warning: #D97706;
  --brandflow-error: #DC2626;
  
  /* Neutral Colors */
  --brandflow-white: #FFFFFF;
  --brandflow-gray-50: #F9FAFB;
  --brandflow-gray-100: #F3F4F6;
  --brandflow-gray-200: #E5E7EB;
  --brandflow-gray-300: #D1D5DB;
  --brandflow-gray-400: #9CA3AF;
  --brandflow-gray-500: #6B7280;
  --brandflow-gray-600: #4B5563;
  --brandflow-gray-700: #374151;
  --brandflow-gray-800: #1F2937;
  --brandflow-gray-900: #111827;
  
  /* Spacing */
  --brandflow-spacing-xs: 4px;
  --brandflow-spacing-sm: 8px;
  --brandflow-spacing-md: 16px;
  --brandflow-spacing-lg: 24px;
  --brandflow-spacing-xl: 32px;
  --brandflow-spacing-xxl: 48px;
  
  /* Border Radius */
  --brandflow-radius-sm: 4px;
  --brandflow-radius-md: 8px;
  --brandflow-radius-lg: 12px;
  --brandflow-radius-xl: 16px;
  
  /* Shadows */
  --brandflow-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --brandflow-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --brandflow-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  
  /* Typography */
  --brandflow-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --brandflow-font-size-xs: 12px;
  --brandflow-font-size-sm: 14px;
  --brandflow-font-size-md: 16px;
  --brandflow-font-size-lg: 18px;
  --brandflow-font-size-xl: 20px;
  --brandflow-font-size-xxl: 24px;
  
  /* Transitions */
  --brandflow-transition: all 0.2s ease;
}

/* Dark Theme Support */
.spectrum--dark {
  --brandflow-primary: #4F9EFF;
  --brandflow-secondary: #9CA3AF;
  --brandflow-white: #1F2937;
  --brandflow-gray-50: #374151;
  --brandflow-gray-100: #4B5563;
  --brandflow-gray-900: #F9FAFB;
}

/* Global Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--brandflow-font-family);
  font-size: var(--brandflow-font-size-md);
  line-height: 1.5;
  color: var(--brandflow-gray-900);
  background-color: var(--brandflow-white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* BrandFlow App Container */
.brandflow-app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Loading Screen */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: var(--brandflow-white);
  gap: var(--brandflow-spacing-lg);
}

.loading-spinner {
  position: relative;
  width: 48px;
  height: 48px;
}

.spinner-ring {
  width: 48px;
  height: 48px;
  border: 4px solid var(--brandflow-gray-200);
  border-top: 4px solid var(--brandflow-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-title {
  margin: 0;
  font-size: var(--brandflow-font-size-xl);
  font-weight: 600;
  color: var(--brandflow-gray-900);
}

.loading-subtitle {
  margin: 0;
  font-size: var(--brandflow-font-size-sm);
  color: var(--brandflow-gray-500);
  text-align: center;
}

/* Error Container */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: var(--brandflow-white);
  gap: var(--brandflow-spacing-lg);
  padding: var(--brandflow-spacing-xl);
  text-align: center;
}

.error-icon {
  font-size: 48px;
  opacity: 0.6;
}

.error-title {
  margin: 0;
  font-size: var(--brandflow-font-size-xl);
  font-weight: 600;
  color: var(--brandflow-gray-900);
}

.error-message {
  margin: 0;
  font-size: var(--brandflow-font-size-md);
  color: var(--brandflow-gray-600);
  max-width: 400px;
  line-height: 1.6;
}

/* Navigation Tabs */
.navigation-tabs {
  display: flex;
  border-bottom: 1px solid var(--brandflow-gray-200);
  background: var(--brandflow-gray-50);
  overflow-x: auto;
}

.navigation-tab {
  display: flex;
  align-items: center;
  gap: var(--brandflow-spacing-sm);
  padding: var(--brandflow-spacing-md) var(--brandflow-spacing-lg);
  border: none;
  background: transparent;
  color: var(--brandflow-gray-600);
  font-size: var(--brandflow-font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: var(--brandflow-transition);
  white-space: nowrap;
  border-bottom: 2px solid transparent;
}

.navigation-tab:hover:not(:disabled) {
  color: var(--brandflow-primary);
  background: var(--brandflow-white);
}

.navigation-tab.active {
  color: var(--brandflow-primary);
  background: var(--brandflow-white);
  border-bottom-color: var(--brandflow-primary);
}

.navigation-tab:disabled {
  color: var(--brandflow-gray-400);
  cursor: not-allowed;
  opacity: 0.6;
}

.navigation-tab-icon {
  font-size: 16px;
}

/* Platform Cards */
.platform-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--brandflow-spacing-md);
  margin-top: var(--brandflow-spacing-md);
}

.platform-card {
  background: var(--brandflow-white);
  border: 2px solid var(--brandflow-gray-200);
  border-radius: var(--brandflow-radius-lg);
  padding: var(--brandflow-spacing-lg);
  cursor: pointer;
  transition: var(--brandflow-transition);
  position: relative;
}

.platform-card:hover {
  border-color: var(--brandflow-primary);
  box-shadow: var(--brandflow-shadow-md);
}

.platform-card.selected {
  border-color: var(--brandflow-primary);
  background: rgba(2, 101, 220, 0.02);
}

.platform-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--brandflow-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-bottom: var(--brandflow-spacing-sm);
  background: var(--brandflow-gray-50);
}

.compliance-indicator {
  position: absolute;
  top: var(--brandflow-spacing-sm);
  right: var(--brandflow-spacing-sm);
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.compliance-indicator.compliant {
  background: var(--brandflow-success);
}

.compliance-indicator.warning {
  background: var(--brandflow-warning);
}

.compliance-indicator.error {
  background: var(--brandflow-error);
}

/* Brand Kit Components */
.upload-zone {
  border: 2px dashed var(--brandflow-gray-300);
  border-radius: var(--brandflow-radius-lg);
  padding: var(--brandflow-spacing-xxl);
  text-align: center;
  background: var(--brandflow-gray-50);
  cursor: pointer;
  transition: var(--brandflow-transition);
}

.upload-zone:hover {
  border-color: var(--brandflow-primary);
  background: rgba(2, 101, 220, 0.02);
}

.upload-zone.drag-over {
  border-color: var(--brandflow-primary);
  background: rgba(2, 101, 220, 0.05);
}

.upload-zone.error {
  border-color: var(--brandflow-error);
  background: rgba(220, 38, 38, 0.02);
}

.color-palette {
  display: flex;
  gap: var(--brandflow-spacing-sm);
  margin-top: var(--brandflow-spacing-sm);
  flex-wrap: wrap;
}

.color-swatch {
  width: 40px;
  height: 40px;
  border-radius: var(--brandflow-radius-sm);
  border: 1px solid var(--brandflow-gray-200);
  cursor: pointer;
  transition: var(--brandflow-transition);
  position: relative;
}

.color-swatch:hover {
  transform: scale(1.1);
  box-shadow: var(--brandflow-shadow-md);
}

.color-swatch::after {
  content: attr(title);
  position: absolute;
  bottom: -24px;
  left: 50%;
  transform: translateX(-50%);
  font-size: var(--brandflow-font-size-xs);
  color: var(--brandflow-gray-600);
  white-space: nowrap;
  opacity: 0;
  transition: var(--brandflow-transition);
}

.color-swatch:hover::after {
  opacity: 1;
}

/* Asset Grid */
.asset-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: var(--brandflow-spacing-md);
  margin-top: var(--brandflow-spacing-md);
}

.asset-preview {
  aspect-ratio: 1;
  border-radius: var(--brandflow-radius-md);
  background: var(--brandflow-gray-50);
  border: 1px solid var(--brandflow-gray-200);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
  transition: var(--brandflow-transition);
}

.asset-preview:hover {
  box-shadow: var(--brandflow-shadow-md);
}

.asset-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Variation Generator */
.variation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--brandflow-spacing-lg);
  margin-top: var(--brandflow-spacing-md);
}

.variation-preview {
  background: var(--brandflow-white);
  border: 1px solid var(--brandflow-gray-200);
  border-radius: var(--brandflow-radius-lg);
  overflow: hidden;
  transition: var(--brandflow-transition);
}

.variation-preview:hover {
  box-shadow: var(--brandflow-shadow-md);
}

.variation-image {
  aspect-ratio: 16/9;
  background: var(--brandflow-gray-100);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--brandflow-gray-500);
}

.variation-info {
  padding: var(--brandflow-spacing-md);
}

.variation-actions {
  padding: var(--brandflow-spacing-md);
  border-top: 1px solid var(--brandflow-gray-200);
  display: flex;
  gap: var(--brandflow-spacing-sm);
}

/* Export Queue */
.export-queue {
  background: var(--brandflow-white);
  border: 1px solid var(--brandflow-gray-200);
  border-radius: var(--brandflow-radius-lg);
  overflow: hidden;
}

.export-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--brandflow-spacing-md);
  border-bottom: 1px solid var(--brandflow-gray-200);
}

.export-item:last-child {
  border-bottom: none;
}

.export-progress {
  width: 100%;
  max-width: 200px;
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.font-weight-normal {
  font-weight: 400;
}

.font-weight-medium {
  font-weight: 500;
}

.font-weight-semibold {
  font-weight: 600;
}

.font-weight-bold {
  font-weight: 700;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-75 {
  opacity: 0.75;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .platform-grid {
    grid-template-columns: 1fr;
  }
  
  .variation-grid {
    grid-template-columns: 1fr;
  }
  
  .asset-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }
  
  .navigation-tabs {
    padding: 0 var(--brandflow-spacing-sm);
  }
  
  .navigation-tab {
    padding: var(--brandflow-spacing-sm) var(--brandflow-spacing-md);
    font-size: var(--brandflow-font-size-xs);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .spinner-ring {
    animation: none;
  }
}

/* Focus Styles */
button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid var(--brandflow-primary);
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  .brandflow-app {
    height: auto;
  }
  
  .loading-container,
  .error-container {
    display: none;
  }
}
