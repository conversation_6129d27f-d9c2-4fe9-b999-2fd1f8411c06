// Simple in-memory project model for demo purposes

class Project {
  constructor(data) {
    this.id = data.id || 'project-' + Date.now();
    this.name = data.name || 'Untitled Project';
    this.brandId = data.brandId || null;
    this.status = data.status || 'draft'; // draft, in-progress, completed
    this.platforms = data.platforms || [];
    this.assets = data.assets || [];
    this.optimizations = data.optimizations || [];
    this.complianceScore = data.complianceScore || null;
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
  }

  // Static methods for CRUD operations
  static projects = new Map();

  static create(data) {
    const project = new Project(data);
    this.projects.set(project.id, project);
    return project;
  }

  static findById(id) {
    return this.projects.get(id) || null;
  }

  static findAll() {
    return Array.from(this.projects.values());
  }

  static findByBrandId(brandId) {
    return Array.from(this.projects.values()).filter(p => p.brandId === brandId);
  }

  static update(id, data) {
    const project = this.projects.get(id);
    if (!project) return null;

    Object.assign(project, data);
    project.updatedAt = new Date();
    return project;
  }

  static delete(id) {
    return this.projects.delete(id);
  }

  // Instance methods
  addAsset(asset) {
    this.assets.push({
      id: 'asset-' + Date.now(),
      ...asset,
      addedAt: new Date()
    });
    this.updatedAt = new Date();
  }

  addOptimization(optimization) {
    this.optimizations.push({
      id: 'opt-' + Date.now(),
      ...optimization,
      createdAt: new Date()
    });
    this.updatedAt = new Date();
  }

  toJSON() {
    return {
      id: this.id,
      name: this.name,
      brandId: this.brandId,
      status: this.status,
      platforms: this.platforms,
      assets: this.assets,
      optimizations: this.optimizations,
      complianceScore: this.complianceScore,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

module.exports = Project;
