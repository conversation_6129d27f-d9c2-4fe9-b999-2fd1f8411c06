// Simple in-memory brand model for demo purposes
// In production, this would use a proper database like MongoDB or PostgreSQL

class Brand {
  constructor(data) {
    this.id = data.id || 'brand-' + Date.now();
    this.name = data.name || 'Untitled Brand';
    this.colors = data.colors || {
      primary: '#0265DC',
      secondary: '#6B7280',
      accent: '#059669',
      neutral: ['#FFFFFF', '#F9FAFB', '#E5E7EB', '#111827']
    };
    this.fonts = data.fonts || {
      primary: 'Arial',
      secondary: 'Helvetica',
      fallback: 'sans-serif'
    };
    this.logos = data.logos || {
      primary: null,
      secondary: null,
      icon: null
    };
    this.guidelines = data.guidelines || {
      spacing: '8px grid system',
      borderRadius: '8px',
      shadows: 'subtle elevation',
      voice: 'professional and friendly'
    };
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
  }

  // Static methods for CRUD operations
  static brands = new Map();

  static create(data) {
    const brand = new Brand(data);
    this.brands.set(brand.id, brand);
    return brand;
  }

  static findById(id) {
    return this.brands.get(id) || null;
  }

  static findAll() {
    return Array.from(this.brands.values());
  }

  static update(id, data) {
    const brand = this.brands.get(id);
    if (!brand) return null;

    Object.assign(brand, data);
    brand.updatedAt = new Date();
    return brand;
  }

  static delete(id) {
    return this.brands.delete(id);
  }

  // Instance methods
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      colors: this.colors,
      fonts: this.fonts,
      logos: this.logos,
      guidelines: this.guidelines,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

module.exports = Brand;
