import React from 'react';
import { View, Flex, Text } from '@adobe/react-spectrum';
import styled from 'styled-components';

interface Tab {
  id: string;
  label: string;
  icon: string;
  disabled: boolean;
}

interface NavigationTabsProps {
  tabs: Tab[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

const TabsContainer = styled(View)`
  display: flex;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
  overflow-x: auto;
`;

const TabButton = styled.button<{ active: boolean; disabled: boolean }>`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};
  border: none;
  background: ${props => props.active ? props.theme.colors.background : 'transparent'};
  color: ${props => {
    if (props.disabled) return props.theme.colors.textSecondary;
    return props.active ? props.theme.colors.primary : props.theme.colors.text;
  }};
  font-size: ${props => props.theme.spacing.sm};
  font-weight: 500;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  transition: ${props => props.theme.transition};
  white-space: nowrap;
  border-bottom: 2px solid ${props => props.active ? props.theme.colors.primary : 'transparent'};
  opacity: ${props => props.disabled ? 0.6 : 1};

  &:hover:not(:disabled) {
    color: ${props => props.theme.colors.primary};
    background: ${props => props.theme.colors.background};
  }
`;

const TabIcon = styled.span`
  font-size: 16px;
`;

const NavigationTabs: React.FC<NavigationTabsProps> = ({
  tabs,
  activeTab,
  onTabChange
}) => {
  return (
    <TabsContainer>
      {tabs.map((tab) => (
        <TabButton
          key={tab.id}
          active={activeTab === tab.id}
          disabled={tab.disabled}
          onClick={() => !tab.disabled && onTabChange(tab.id)}
        >
          <TabIcon>{tab.icon}</TabIcon>
          <Text>{tab.label}</Text>
        </TabButton>
      ))}
    </TabsContainer>
  );
};

export default NavigationTabs;
