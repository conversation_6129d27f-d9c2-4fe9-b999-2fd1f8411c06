import React, { useState, useCallback, useRef } from 'react';
import { 
  View, 
  Flex, 
  Heading, 
  Text, 
  Button, 
  ProgressBar,
  Well,
  ActionButton,
  TextField,
  ColorField,
  Picker,
  Item
} from '@adobe/react-spectrum';
import styled from 'styled-components';

// Icons
import Upload from '@spectrum-icons/workflow/Upload';
import Delete from '@spectrum-icons/workflow/Delete';
import Edit from '@spectrum-icons/workflow/Edit';
import CheckmarkCircle from '@spectrum-icons/workflow/CheckmarkCircle';

// Services
import { analyzeBrandAssets } from '../services/brandAnalysis';
import { validateBrandKit } from '../utils/brandCompliance';

// Types
interface BrandKit {
  id: string;
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    neutral: string[];
  };
  fonts: {
    primary: string;
    secondary: string;
    weights: string[];
  };
  logos: {
    primary: File | null;
    secondary: File | null;
    icon: File | null;
  };
  guidelines: {
    spacing: number;
    borderRadius: number;
    minLogoSize: number;
    clearSpace: number;
  };
  assets: File[];
  createdAt: Date;
  updatedAt: Date;
}

interface BrandKitUploaderProps {
  onUpload: (files: FileList) => Promise<void>;
  brandKit: BrandKit | null;
  isUploaded: boolean;
}

// Styled Components
const UploadZone = styled(View)<{ isDragOver: boolean; hasError: boolean }>`
  border: 2px dashed ${props => 
    props.hasError ? props.theme.colors.error :
    props.isDragOver ? props.theme.colors.primary : 
    props.theme.colors.border
  };
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.xl};
  text-align: center;
  background: ${props => 
    props.isDragOver ? `${props.theme.colors.primary}10` : 
    props.theme.colors.surface
  };
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: ${props => props.theme.colors.primary};
    background: ${props => `${props.theme.colors.primary}05`};
  }
`;

const BrandPreview = styled(View)`
  background: white;
  border-radius: ${props => props.theme.borderRadius.md};
  padding: ${props => props.theme.spacing.lg};
  box-shadow: ${props => props.theme.shadows.sm};
  margin-top: ${props => props.theme.spacing.md};
`;

const ColorPalette = styled(Flex)`
  gap: ${props => props.theme.spacing.sm};
  margin-top: ${props => props.theme.spacing.sm};
`;

const ColorSwatch = styled(View)<{ color: string }>`
  width: 40px;
  height: 40px;
  border-radius: ${props => props.theme.borderRadius.sm};
  background: ${props => props.color};
  border: 1px solid ${props => props.theme.colors.border};
  cursor: pointer;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.1);
  }
`;

const AssetGrid = styled(View)`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: ${props => props.theme.spacing.md};
  margin-top: ${props => props.theme.spacing.md};
`;

const AssetPreview = styled(View)`
  aspect-ratio: 1;
  border-radius: ${props => props.theme.borderRadius.md};
  background: ${props => props.theme.colors.surface};
  border: 1px solid ${props => props.theme.colors.border};
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const BrandKitUploader: React.FC<BrandKitUploaderProps> = ({
  onUpload,
  brandKit,
  isUploaded
}) => {
  // State
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editedBrandKit, setEditedBrandKit] = useState<Partial<BrandKit> | null>(null);

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Drag and Drop Handlers
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      await handleFileUpload(files);
    }
  }, []);

  // File Upload Handler
  const handleFileUpload = async (files: FileList) => {
    try {
      setIsUploading(true);
      setError(null);
      setUploadProgress(0);

      // Validate files
      const validationResult = validateBrandKit(files);
      if (!validationResult.isValid) {
        throw new Error(validationResult.errors.join(', '));
      }

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 200);

      // Analyze brand assets using AI
      const analysisResult = await analyzeBrandAssets(files);
      
      // Complete upload
      await onUpload(files);
      
      setUploadProgress(100);
      clearInterval(progressInterval);
      
      // Reset state after successful upload
      setTimeout(() => {
        setIsUploading(false);
        setUploadProgress(0);
      }, 1000);

    } catch (error) {
      console.error('Upload failed:', error);
      setError(error instanceof Error ? error.message : 'Upload failed');
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  // File Input Handler
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileUpload(files);
    }
  };

  // Edit Handlers
  const handleEditStart = () => {
    setIsEditing(true);
    setEditedBrandKit(brandKit ? { ...brandKit } : null);
  };

  const handleEditSave = async () => {
    if (editedBrandKit) {
      // Save edited brand kit
      // This would typically call an API to update the brand kit
      console.log('Saving edited brand kit:', editedBrandKit);
      setIsEditing(false);
    }
  };

  const handleEditCancel = () => {
    setIsEditing(false);
    setEditedBrandKit(null);
  };

  // Render Upload Zone
  const renderUploadZone = () => (
    <UploadZone
      isDragOver={isDragOver}
      hasError={!!error}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onClick={() => fileInputRef.current?.click()}
    >
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept=".png,.jpg,.jpeg,.svg,.pdf,.ai,.psd"
        style={{ display: 'none' }}
        onChange={handleFileInputChange}
      />
      
      <Flex direction="column" alignItems="center" gap="size-200">
        <Upload size="XL" />
        <Heading level={3}>Upload Brand Assets</Heading>
        <Text>
          Drag and drop your brand assets here, or click to browse
        </Text>
        <Text slot="description">
          Supported formats: PNG, JPG, SVG, PDF, AI, PSD (Max 10MB each)
        </Text>
        
        {error && (
          <Well>
            <Text color="negative">{error}</Text>
          </Well>
        )}
        
        {isUploading && (
          <View width="100%" maxWidth="300px">
            <ProgressBar
              label="Uploading and analyzing assets..."
              value={uploadProgress}
              maxValue={100}
              showValueLabel
            />
          </View>
        )}
      </Flex>
    </UploadZone>
  );

  // Render Brand Kit Preview
  const renderBrandKitPreview = () => {
    if (!brandKit) return null;

    return (
      <BrandPreview>
        <Flex direction="column" gap="size-200">
          <Flex justifyContent="space-between" alignItems="center">
            <Heading level={3}>
              <CheckmarkCircle color="positive" />
              {brandKit.name || 'Brand Kit'}
            </Heading>
            <Flex gap="size-100">
              <ActionButton onPress={handleEditStart}>
                <Edit />
                <Text>Edit</Text>
              </ActionButton>
              <ActionButton onPress={() => setError('Delete functionality coming soon')}>
                <Delete />
                <Text>Delete</Text>
              </ActionButton>
            </Flex>
          </Flex>

          {/* Color Palette */}
          <View>
            <Text>Brand Colors</Text>
            <ColorPalette>
              <ColorSwatch color={brandKit.colors.primary} title="Primary" />
              <ColorSwatch color={brandKit.colors.secondary} title="Secondary" />
              <ColorSwatch color={brandKit.colors.accent} title="Accent" />
              {brandKit.colors.neutral.map((color, index) => (
                <ColorSwatch key={index} color={color} title={`Neutral ${index + 1}`} />
              ))}
            </ColorPalette>
          </View>

          {/* Typography */}
          <View>
            <Text>Typography</Text>
            <Flex direction="column" gap="size-100">
              <Text>Primary Font: {brandKit.fonts.primary}</Text>
              <Text>Secondary Font: {brandKit.fonts.secondary}</Text>
            </Flex>
          </View>

          {/* Assets Preview */}
          {brandKit.assets.length > 0 && (
            <View>
              <Text>Brand Assets ({brandKit.assets.length})</Text>
              <AssetGrid>
                {brandKit.assets.slice(0, 6).map((asset, index) => (
                  <AssetPreview key={index}>
                    <img 
                      src={URL.createObjectURL(asset)} 
                      alt={`Brand asset ${index + 1}`}
                      onLoad={(e) => URL.revokeObjectURL((e.target as HTMLImageElement).src)}
                    />
                  </AssetPreview>
                ))}
                {brandKit.assets.length > 6 && (
                  <AssetPreview>
                    <Text>+{brandKit.assets.length - 6} more</Text>
                  </AssetPreview>
                )}
              </AssetGrid>
            </View>
          )}
        </Flex>
      </BrandPreview>
    );
  };

  // Render Edit Form
  const renderEditForm = () => {
    if (!isEditing || !editedBrandKit) return null;

    return (
      <BrandPreview>
        <Flex direction="column" gap="size-200">
          <Heading level={3}>Edit Brand Kit</Heading>
          
          <TextField
            label="Brand Kit Name"
            value={editedBrandKit.name || ''}
            onChange={(value) => setEditedBrandKit(prev => ({ ...prev, name: value }))}
          />

          <Flex gap="size-200">
            <ColorField
              label="Primary Color"
              value={editedBrandKit.colors?.primary || '#0265DC'}
              onChange={(value) => setEditedBrandKit(prev => ({
                ...prev,
                colors: { ...prev?.colors, primary: value } as any
              }))}
            />
            <ColorField
              label="Secondary Color"
              value={editedBrandKit.colors?.secondary || '#6B7280'}
              onChange={(value) => setEditedBrandKit(prev => ({
                ...prev,
                colors: { ...prev?.colors, secondary: value } as any
              }))}
            />
          </Flex>

          <Flex gap="size-200">
            <TextField
              label="Primary Font"
              value={editedBrandKit.fonts?.primary || ''}
              onChange={(value) => setEditedBrandKit(prev => ({
                ...prev,
                fonts: { ...prev?.fonts, primary: value } as any
              }))}
            />
            <TextField
              label="Secondary Font"
              value={editedBrandKit.fonts?.secondary || ''}
              onChange={(value) => setEditedBrandKit(prev => ({
                ...prev,
                fonts: { ...prev?.fonts, secondary: value } as any
              }))}
            />
          </Flex>

          <Flex gap="size-200" justifyContent="end">
            <Button variant="secondary" onPress={handleEditCancel}>
              Cancel
            </Button>
            <Button variant="cta" onPress={handleEditSave}>
              Save Changes
            </Button>
          </Flex>
        </Flex>
      </BrandPreview>
    );
  };

  return (
    <View padding="size-200">
      <Flex direction="column" gap="size-200">
        <Heading level={2}>Brand Kit Setup</Heading>
        <Text>
          Upload your brand assets to ensure consistent styling across all platforms.
          Our AI will analyze your assets and extract brand guidelines automatically.
        </Text>

        {!isUploaded && renderUploadZone()}
        {isUploaded && !isEditing && renderBrandKitPreview()}
        {isEditing && renderEditForm()}
      </Flex>
    </View>
  );
};

export default BrandKitUploader;
